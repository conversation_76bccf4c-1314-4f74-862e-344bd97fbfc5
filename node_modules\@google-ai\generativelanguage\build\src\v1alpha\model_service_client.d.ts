import type * as gax from 'google-gax';
import type { Callback, CallOptions, Descriptors, ClientOptions, LROperation, PaginationCallback } from 'google-gax';
import { Transform } from 'stream';
import * as protos from '../../protos/protos';
/**
 *  Provides methods for getting metadata information about Generative Models.
 * @class
 * @memberof v1alpha
 */
export declare class ModelServiceClient {
    private _terminated;
    private _opts;
    private _providedCustomServicePath;
    private _gaxModule;
    private _gaxGrpc;
    private _protos;
    private _defaults;
    private _universeDomain;
    private _servicePath;
    private _log;
    auth: gax.GoogleAuth;
    descriptors: Descriptors;
    warn: (code: string, message: string, warnType?: string) => void;
    innerApiCalls: {
        [name: string]: Function;
    };
    pathTemplates: {
        [name: string]: gax.PathTemplate;
    };
    operationsClient: gax.OperationsClient;
    modelServiceStub?: Promise<{
        [name: string]: Function;
    }>;
    /**
     * Construct an instance of ModelServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new ModelServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts?: ClientOptions, gaxInstance?: typeof gax | typeof gax.fallback);
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize(): Promise<{
        [name: string]: Function;
    }>;
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath(): string;
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint(): string;
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint(): string;
    get universeDomain(): string;
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port(): number;
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes(): never[];
    getProjectId(): Promise<string>;
    getProjectId(callback: Callback<string, undefined, undefined>): void;
    /**
     * Gets information about a specific `Model` such as its version number, token
     * limits,
     * [parameters](https://ai.google.dev/gemini-api/docs/models/generative-models#model-parameters)
     * and other metadata. Refer to the [Gemini models
     * guide](https://ai.google.dev/gemini-api/docs/models/gemini) for detailed
     * model information.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the model.
     *
     *   This name should match a model name returned by the `ListModels` method.
     *
     *   Format: `models/{model}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1alpha.Model|Model}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/model_service.get_model.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_ModelService_GetModel_async
     */
    getModel(request?: protos.google.ai.generativelanguage.v1alpha.IGetModelRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1alpha.IModel,
        protos.google.ai.generativelanguage.v1alpha.IGetModelRequest | undefined,
        {} | undefined
    ]>;
    getModel(request: protos.google.ai.generativelanguage.v1alpha.IGetModelRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1alpha.IModel, protos.google.ai.generativelanguage.v1alpha.IGetModelRequest | null | undefined, {} | null | undefined>): void;
    getModel(request: protos.google.ai.generativelanguage.v1alpha.IGetModelRequest, callback: Callback<protos.google.ai.generativelanguage.v1alpha.IModel, protos.google.ai.generativelanguage.v1alpha.IGetModelRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets information about a specific TunedModel.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the model.
     *
     *   Format: `tunedModels/my-model-id`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1alpha.TunedModel|TunedModel}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/model_service.get_tuned_model.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_ModelService_GetTunedModel_async
     */
    getTunedModel(request?: protos.google.ai.generativelanguage.v1alpha.IGetTunedModelRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1alpha.ITunedModel,
        (protos.google.ai.generativelanguage.v1alpha.IGetTunedModelRequest | undefined),
        {} | undefined
    ]>;
    getTunedModel(request: protos.google.ai.generativelanguage.v1alpha.IGetTunedModelRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1alpha.ITunedModel, protos.google.ai.generativelanguage.v1alpha.IGetTunedModelRequest | null | undefined, {} | null | undefined>): void;
    getTunedModel(request: protos.google.ai.generativelanguage.v1alpha.IGetTunedModelRequest, callback: Callback<protos.google.ai.generativelanguage.v1alpha.ITunedModel, protos.google.ai.generativelanguage.v1alpha.IGetTunedModelRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates a tuned model.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.ai.generativelanguage.v1alpha.TunedModel} request.tunedModel
     *   Required. The tuned model to update.
     * @param {google.protobuf.FieldMask} [request.updateMask]
     *   Optional. The list of fields to update.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1alpha.TunedModel|TunedModel}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/model_service.update_tuned_model.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_ModelService_UpdateTunedModel_async
     */
    updateTunedModel(request?: protos.google.ai.generativelanguage.v1alpha.IUpdateTunedModelRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1alpha.ITunedModel,
        (protos.google.ai.generativelanguage.v1alpha.IUpdateTunedModelRequest | undefined),
        {} | undefined
    ]>;
    updateTunedModel(request: protos.google.ai.generativelanguage.v1alpha.IUpdateTunedModelRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1alpha.ITunedModel, protos.google.ai.generativelanguage.v1alpha.IUpdateTunedModelRequest | null | undefined, {} | null | undefined>): void;
    updateTunedModel(request: protos.google.ai.generativelanguage.v1alpha.IUpdateTunedModelRequest, callback: Callback<protos.google.ai.generativelanguage.v1alpha.ITunedModel, protos.google.ai.generativelanguage.v1alpha.IUpdateTunedModelRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Deletes a tuned model.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the model.
     *   Format: `tunedModels/my-model-id`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/model_service.delete_tuned_model.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_ModelService_DeleteTunedModel_async
     */
    deleteTunedModel(request?: protos.google.ai.generativelanguage.v1alpha.IDeleteTunedModelRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        (protos.google.ai.generativelanguage.v1alpha.IDeleteTunedModelRequest | undefined),
        {} | undefined
    ]>;
    deleteTunedModel(request: protos.google.ai.generativelanguage.v1alpha.IDeleteTunedModelRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1alpha.IDeleteTunedModelRequest | null | undefined, {} | null | undefined>): void;
    deleteTunedModel(request: protos.google.ai.generativelanguage.v1alpha.IDeleteTunedModelRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1alpha.IDeleteTunedModelRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates a tuned model.
     * Check intermediate tuning progress (if any) through the
     * [google.longrunning.Operations] service.
     *
     * Access status and results through the Operations service.
     * Example:
     *   GET /v1/tunedModels/az2mb0bpw6i/operations/000-111-222
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} [request.tunedModelId]
     *   Optional. The unique id for the tuned model if specified.
     *   This value should be up to 40 characters, the first character must be a
     *   letter, the last could be a letter or a number. The id must match the
     *   regular expression: `[a-z]([a-z0-9-]{0,38}[a-z0-9])?`.
     * @param {google.ai.generativelanguage.v1alpha.TunedModel} request.tunedModel
     *   Required. The tuned model to create.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/model_service.create_tuned_model.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_ModelService_CreateTunedModel_async
     */
    createTunedModel(request?: protos.google.ai.generativelanguage.v1alpha.ICreateTunedModelRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.ai.generativelanguage.v1alpha.ITunedModel, protos.google.ai.generativelanguage.v1alpha.ICreateTunedModelMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    createTunedModel(request: protos.google.ai.generativelanguage.v1alpha.ICreateTunedModelRequest, options: CallOptions, callback: Callback<LROperation<protos.google.ai.generativelanguage.v1alpha.ITunedModel, protos.google.ai.generativelanguage.v1alpha.ICreateTunedModelMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    createTunedModel(request: protos.google.ai.generativelanguage.v1alpha.ICreateTunedModelRequest, callback: Callback<LROperation<protos.google.ai.generativelanguage.v1alpha.ITunedModel, protos.google.ai.generativelanguage.v1alpha.ICreateTunedModelMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `createTunedModel()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/model_service.create_tuned_model.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_ModelService_CreateTunedModel_async
     */
    checkCreateTunedModelProgress(name: string): Promise<LROperation<protos.google.ai.generativelanguage.v1alpha.TunedModel, protos.google.ai.generativelanguage.v1alpha.CreateTunedModelMetadata>>;
    /**
     * Lists the [`Model`s](https://ai.google.dev/gemini-api/docs/models/gemini)
     * available through the Gemini API.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} request.pageSize
     *   The maximum number of `Models` to return (per page).
     *
     *   If unspecified, 50 models will be returned per page.
     *   This method returns at most 1000 models per page, even if you pass a larger
     *   page_size.
     * @param {string} request.pageToken
     *   A page token, received from a previous `ListModels` call.
     *
     *   Provide the `page_token` returned by one request as an argument to the next
     *   request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListModels` must match
     *   the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.ai.generativelanguage.v1alpha.Model|Model}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listModelsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listModels(request?: protos.google.ai.generativelanguage.v1alpha.IListModelsRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1alpha.IModel[],
        protos.google.ai.generativelanguage.v1alpha.IListModelsRequest | null,
        protos.google.ai.generativelanguage.v1alpha.IListModelsResponse
    ]>;
    listModels(request: protos.google.ai.generativelanguage.v1alpha.IListModelsRequest, options: CallOptions, callback: PaginationCallback<protos.google.ai.generativelanguage.v1alpha.IListModelsRequest, protos.google.ai.generativelanguage.v1alpha.IListModelsResponse | null | undefined, protos.google.ai.generativelanguage.v1alpha.IModel>): void;
    listModels(request: protos.google.ai.generativelanguage.v1alpha.IListModelsRequest, callback: PaginationCallback<protos.google.ai.generativelanguage.v1alpha.IListModelsRequest, protos.google.ai.generativelanguage.v1alpha.IListModelsResponse | null | undefined, protos.google.ai.generativelanguage.v1alpha.IModel>): void;
    /**
     * Equivalent to `listModels`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} request.pageSize
     *   The maximum number of `Models` to return (per page).
     *
     *   If unspecified, 50 models will be returned per page.
     *   This method returns at most 1000 models per page, even if you pass a larger
     *   page_size.
     * @param {string} request.pageToken
     *   A page token, received from a previous `ListModels` call.
     *
     *   Provide the `page_token` returned by one request as an argument to the next
     *   request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListModels` must match
     *   the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1alpha.Model|Model} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listModelsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listModelsStream(request?: protos.google.ai.generativelanguage.v1alpha.IListModelsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listModels`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} request.pageSize
     *   The maximum number of `Models` to return (per page).
     *
     *   If unspecified, 50 models will be returned per page.
     *   This method returns at most 1000 models per page, even if you pass a larger
     *   page_size.
     * @param {string} request.pageToken
     *   A page token, received from a previous `ListModels` call.
     *
     *   Provide the `page_token` returned by one request as an argument to the next
     *   request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListModels` must match
     *   the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1alpha.Model|Model}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/model_service.list_models.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_ModelService_ListModels_async
     */
    listModelsAsync(request?: protos.google.ai.generativelanguage.v1alpha.IListModelsRequest, options?: CallOptions): AsyncIterable<protos.google.ai.generativelanguage.v1alpha.IModel>;
    /**
     * Lists created tuned models.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `TunedModels` to return (per page).
     *   The service may return fewer tuned models.
     *
     *   If unspecified, at most 10 tuned models will be returned.
     *   This method returns at most 1000 models per page, even if you pass a larger
     *   page_size.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListTunedModels` call.
     *
     *   Provide the `page_token` returned by one request as an argument to the next
     *   request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListTunedModels`
     *   must match the call that provided the page token.
     * @param {string} [request.filter]
     *   Optional. A filter is a full text search over the tuned model's description
     *   and display name. By default, results will not include tuned models shared
     *   with everyone.
     *
     *   Additional operators:
     *     - owner:me
     *     - writers:me
     *     - readers:me
     *     - readers:everyone
     *
     *   Examples:
     *     "owner:me" returns all tuned models to which caller has owner role
     *     "readers:me" returns all tuned models to which caller has reader role
     *     "readers:everyone" returns all tuned models that are shared with everyone
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.ai.generativelanguage.v1alpha.TunedModel|TunedModel}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listTunedModelsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listTunedModels(request?: protos.google.ai.generativelanguage.v1alpha.IListTunedModelsRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1alpha.ITunedModel[],
        protos.google.ai.generativelanguage.v1alpha.IListTunedModelsRequest | null,
        protos.google.ai.generativelanguage.v1alpha.IListTunedModelsResponse
    ]>;
    listTunedModels(request: protos.google.ai.generativelanguage.v1alpha.IListTunedModelsRequest, options: CallOptions, callback: PaginationCallback<protos.google.ai.generativelanguage.v1alpha.IListTunedModelsRequest, protos.google.ai.generativelanguage.v1alpha.IListTunedModelsResponse | null | undefined, protos.google.ai.generativelanguage.v1alpha.ITunedModel>): void;
    listTunedModels(request: protos.google.ai.generativelanguage.v1alpha.IListTunedModelsRequest, callback: PaginationCallback<protos.google.ai.generativelanguage.v1alpha.IListTunedModelsRequest, protos.google.ai.generativelanguage.v1alpha.IListTunedModelsResponse | null | undefined, protos.google.ai.generativelanguage.v1alpha.ITunedModel>): void;
    /**
     * Equivalent to `listTunedModels`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `TunedModels` to return (per page).
     *   The service may return fewer tuned models.
     *
     *   If unspecified, at most 10 tuned models will be returned.
     *   This method returns at most 1000 models per page, even if you pass a larger
     *   page_size.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListTunedModels` call.
     *
     *   Provide the `page_token` returned by one request as an argument to the next
     *   request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListTunedModels`
     *   must match the call that provided the page token.
     * @param {string} [request.filter]
     *   Optional. A filter is a full text search over the tuned model's description
     *   and display name. By default, results will not include tuned models shared
     *   with everyone.
     *
     *   Additional operators:
     *     - owner:me
     *     - writers:me
     *     - readers:me
     *     - readers:everyone
     *
     *   Examples:
     *     "owner:me" returns all tuned models to which caller has owner role
     *     "readers:me" returns all tuned models to which caller has reader role
     *     "readers:everyone" returns all tuned models that are shared with everyone
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1alpha.TunedModel|TunedModel} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listTunedModelsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listTunedModelsStream(request?: protos.google.ai.generativelanguage.v1alpha.IListTunedModelsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listTunedModels`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `TunedModels` to return (per page).
     *   The service may return fewer tuned models.
     *
     *   If unspecified, at most 10 tuned models will be returned.
     *   This method returns at most 1000 models per page, even if you pass a larger
     *   page_size.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListTunedModels` call.
     *
     *   Provide the `page_token` returned by one request as an argument to the next
     *   request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListTunedModels`
     *   must match the call that provided the page token.
     * @param {string} [request.filter]
     *   Optional. A filter is a full text search over the tuned model's description
     *   and display name. By default, results will not include tuned models shared
     *   with everyone.
     *
     *   Additional operators:
     *     - owner:me
     *     - writers:me
     *     - readers:me
     *     - readers:everyone
     *
     *   Examples:
     *     "owner:me" returns all tuned models to which caller has owner role
     *     "readers:me" returns all tuned models to which caller has reader role
     *     "readers:everyone" returns all tuned models that are shared with everyone
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1alpha.TunedModel|TunedModel}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/model_service.list_tuned_models.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_ModelService_ListTunedModels_async
     */
    listTunedModelsAsync(request?: protos.google.ai.generativelanguage.v1alpha.IListTunedModelsRequest, options?: CallOptions): AsyncIterable<protos.google.ai.generativelanguage.v1alpha.ITunedModel>;
    /**
     * Gets the latest state of a long-running operation.  Clients can use this
     * method to poll the operation result at intervals as recommended by the API
     * service.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     *   for the details.
     * @param {function(?Error, ?Object)=} callback
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing
     *   {@link google.longrunning.Operation | google.longrunning.Operation}.
     * @return {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     * {@link google.longrunning.Operation | google.longrunning.Operation}.
     * The promise has a method named "cancel" which cancels the ongoing API call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * const name = '';
     * const [response] = await client.getOperation({name});
     * // doThingsWith(response)
     * ```
     */
    getOperation(request: protos.google.longrunning.GetOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.longrunning.Operation, protos.google.longrunning.GetOperationRequest, {} | null | undefined>, callback?: Callback<protos.google.longrunning.Operation, protos.google.longrunning.GetOperationRequest, {} | null | undefined>): Promise<[protos.google.longrunning.Operation]>;
    /**
     * Lists operations that match the specified filter in the request. If the
     * server doesn't support this method, it returns `UNIMPLEMENTED`. Returns an iterable object.
     *
     * For-await-of syntax is used with the iterable to recursively get response element on-demand.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation collection.
     * @param {string} request.filter - The standard list filter.
     * @param {number=} request.pageSize -
     *   The maximum number of resources contained in the underlying API
     *   response. If page streaming is performed per-resource, this
     *   parameter does not affect the return value. If page streaming is
     *   performed per-page, this determines the maximum number of
     *   resources in a page.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     *   details.
     * @returns {Object}
     *   An iterable Object that conforms to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | iteration protocols}.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * for await (const response of client.listOperationsAsync(request));
     * // doThingsWith(response)
     * ```
     */
    listOperationsAsync(request: protos.google.longrunning.ListOperationsRequest, options?: gax.CallOptions): AsyncIterable<protos.google.longrunning.IOperation>;
    /**
     * Starts asynchronous cancellation on a long-running operation.  The server
     * makes a best effort to cancel the operation, but success is not
     * guaranteed.  If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.  Clients can use
     * {@link Operations.GetOperation} or
     * other methods to check whether the cancellation succeeded or whether the
     * operation completed despite cancellation. On successful cancellation,
     * the operation is not deleted; instead, it becomes an operation with
     * an {@link Operation.error} value with a {@link google.rpc.Status.code} of
     * 1, corresponding to `Code.CANCELLED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be cancelled.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     * details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.cancelOperation({name: ''});
     * ```
     */
    cancelOperation(request: protos.google.longrunning.CancelOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.longrunning.CancelOperationRequest, protos.google.protobuf.Empty, {} | undefined | null>, callback?: Callback<protos.google.longrunning.CancelOperationRequest, protos.google.protobuf.Empty, {} | undefined | null>): Promise<protos.google.protobuf.Empty>;
    /**
     * Deletes a long-running operation. This method indicates that the client is
     * no longer interested in the operation result. It does not cancel the
     * operation. If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be deleted.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     * for the details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.deleteOperation({name: ''});
     * ```
     */
    deleteOperation(request: protos.google.longrunning.DeleteOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.protobuf.Empty, protos.google.longrunning.DeleteOperationRequest, {} | null | undefined>, callback?: Callback<protos.google.protobuf.Empty, protos.google.longrunning.DeleteOperationRequest, {} | null | undefined>): Promise<protos.google.protobuf.Empty>;
    /**
     * Return a fully-qualified cachedContent resource name string.
     *
     * @param {string} id
     * @returns {string} Resource name string.
     */
    cachedContentPath(id: string): string;
    /**
     * Parse the id from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the id.
     */
    matchIdFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Return a fully-qualified chunk resource name string.
     *
     * @param {string} corpus
     * @param {string} document
     * @param {string} chunk
     * @returns {string} Resource name string.
     */
    chunkPath(corpus: string, document: string, chunk: string): string;
    /**
     * Parse the corpus from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromChunkName(chunkName: string): string | number;
    /**
     * Parse the document from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromChunkName(chunkName: string): string | number;
    /**
     * Parse the chunk from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the chunk.
     */
    matchChunkFromChunkName(chunkName: string): string | number;
    /**
     * Return a fully-qualified corpus resource name string.
     *
     * @param {string} corpus
     * @returns {string} Resource name string.
     */
    corpusPath(corpus: string): string;
    /**
     * Parse the corpus from Corpus resource.
     *
     * @param {string} corpusName
     *   A fully-qualified path representing Corpus resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromCorpusName(corpusName: string): string | number;
    /**
     * Return a fully-qualified corpusPermission resource name string.
     *
     * @param {string} corpus
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    corpusPermissionPath(corpus: string, permission: string): string;
    /**
     * Parse the corpus from CorpusPermission resource.
     *
     * @param {string} corpusPermissionName
     *   A fully-qualified path representing corpus_permission resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromCorpusPermissionName(corpusPermissionName: string): string | number;
    /**
     * Parse the permission from CorpusPermission resource.
     *
     * @param {string} corpusPermissionName
     *   A fully-qualified path representing corpus_permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromCorpusPermissionName(corpusPermissionName: string): string | number;
    /**
     * Return a fully-qualified document resource name string.
     *
     * @param {string} corpus
     * @param {string} document
     * @returns {string} Resource name string.
     */
    documentPath(corpus: string, document: string): string;
    /**
     * Parse the corpus from Document resource.
     *
     * @param {string} documentName
     *   A fully-qualified path representing Document resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromDocumentName(documentName: string): string | number;
    /**
     * Parse the document from Document resource.
     *
     * @param {string} documentName
     *   A fully-qualified path representing Document resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromDocumentName(documentName: string): string | number;
    /**
     * Return a fully-qualified file resource name string.
     *
     * @param {string} file
     * @returns {string} Resource name string.
     */
    filePath(file: string): string;
    /**
     * Parse the file from File resource.
     *
     * @param {string} fileName
     *   A fully-qualified path representing File resource.
     * @returns {string} A string representing the file.
     */
    matchFileFromFileName(fileName: string): string | number;
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(model: string): string;
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName: string): string | number;
    /**
     * Return a fully-qualified tunedModel resource name string.
     *
     * @param {string} tuned_model
     * @returns {string} Resource name string.
     */
    tunedModelPath(tunedModel: string): string;
    /**
     * Parse the tuned_model from TunedModel resource.
     *
     * @param {string} tunedModelName
     *   A fully-qualified path representing TunedModel resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelName(tunedModelName: string): string | number;
    /**
     * Return a fully-qualified tunedModelPermission resource name string.
     *
     * @param {string} tuned_model
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    tunedModelPermissionPath(tunedModel: string, permission: string): string;
    /**
     * Parse the tuned_model from TunedModelPermission resource.
     *
     * @param {string} tunedModelPermissionName
     *   A fully-qualified path representing tuned_model_permission resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelPermissionName(tunedModelPermissionName: string): string | number;
    /**
     * Parse the permission from TunedModelPermission resource.
     *
     * @param {string} tunedModelPermissionName
     *   A fully-qualified path representing tuned_model_permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromTunedModelPermissionName(tunedModelPermissionName: string): string | number;
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close(): Promise<void>;
}
