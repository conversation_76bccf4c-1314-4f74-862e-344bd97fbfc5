import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { z } from 'zod';

// Schema for updating a custom role. role_id is not updatable to maintain uniqueness with user_id.
const UpdateCustomRoleSchema = z.object({
  name: z.string().trim().min(1, 'Name is required').max(100, 'Name must be 100 characters or less').optional(),
  description: z.string().trim().max(500, 'Description must be 500 characters or less').optional().nullable(),
}).refine(data => Object.keys(data).length > 0, {
  message: 'At least one field (name or description) must be provided for an update.',
});

interface RouteContext {
  params: Promise<{
    customRoleId: string; // This will be the database UUID of the user_custom_roles entry
  }>;
}

// PUT /api/user/custom-roles/[customRoleId]
// Updates an existing global custom role owned by the authenticated user
export async function PUT(request: NextRequest, { params }: RouteContext) {
  const supabase = await createSupabaseServerClientOnRequest();
  /*
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  */
  const { customRoleId } = await params;
  /*
  if (userError || !user) {
    return NextResponse.json({ error: 'Unauthorized. Please log in.' }, { status: 401 });
  }
  const userId = user.id;
  */
  const userId = '00000000-0000-0000-0000-000000000000'; // Placeholder for testing

  if (!customRoleId || typeof customRoleId !== 'string') {
    return NextResponse.json({ error: 'Invalid Custom Role ID format.' }, { status: 400 });
  }

  let requestBody;
  try {
    requestBody = await request.json();
  } catch (e) {
    return NextResponse.json({ error: 'Invalid JSON request body.' }, { status: 400 });
  }

  const validationResult = UpdateCustomRoleSchema.safeParse(requestBody);
  if (!validationResult.success) {
    return NextResponse.json(
      { error: 'Invalid request body.', issues: validationResult.error.flatten().fieldErrors },
      { status: 400 }
    );
  }

  const updatePayload = validationResult.data;

  try {
    // Verify the custom role exists and belongs to the user, then update
    const { data: updatedCustomRole, error } = await supabase
      .from('user_custom_roles')
      .update({
        ...updatePayload,
        updated_at: new Date().toISOString(), // Manually set updated_at if not handled by DB trigger
      })
      .eq('id', customRoleId)      // Match by the role's own UUID
      .eq('user_id', userId)       // Ensure the user owns this role
      .select()
      .single();

    if (error) {
      console.error('Supabase error updating global custom role:', error);
      // PGRST116 means no row was found for the update (either ID or user_id mismatch)
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Custom role not found or you do not own this role.' }, { status: 404 });
      }
      return NextResponse.json({ error: 'Failed to update custom role.', details: error.message }, { status: 500 });
    }
    
    if (!updatedCustomRole) { // Should be caught by PGRST116, but as a safeguard
        return NextResponse.json({ error: 'Custom role not found after update attempt.' }, { status: 404 });
    }

    return NextResponse.json(updatedCustomRole, { status: 200 });

  } catch (e: Error) {
    console.error('Error in PUT /api/user/custom-roles/[customRoleId]:', e);
    return NextResponse.json({ error: 'An unexpected server error occurred.', details: e.message }, { status: 500 });
  }
}

// DELETE /api/user/custom-roles/[customRoleId]
// Deletes a specific global custom role owned by the authenticated user
export async function DELETE(request: NextRequest, { params }: RouteContext) {
  const supabase = await createSupabaseServerClientOnRequest();
  /*
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  */
  const { customRoleId } = await params;
  /*
  if (userError || !user) {
    return NextResponse.json({ error: 'Unauthorized. Please log in.' }, { status: 401 });
  }
  const userId = user.id;
  */
  const userId = '00000000-0000-0000-0000-000000000000'; // Placeholder for testing

  if (!customRoleId || typeof customRoleId !== 'string') {
    return NextResponse.json({ error: 'Invalid Custom Role ID format.' }, { status: 400 });
  }

  try {
    const { error, count } = await supabase
      .from('user_custom_roles')
      .delete({ count: 'exact' })
      .eq('id', customRoleId)      // Match by the role's own UUID
      .eq('user_id', userId);      // Ensure the user owns this role

    if (error) {
      console.error('Supabase error deleting global custom role:', error);
      return NextResponse.json({ error: 'Failed to delete custom role.', details: error.message }, { status: 500 });
    }

    if (count === 0) {
      return NextResponse.json({ error: 'Custom role not found, you do not own this role, or it was already deleted.' }, { status: 404 });
    }

    // Before returning success, we need to unassign this global custom role from all API keys across all of the user's configurations.
    // This is a critical step for data integrity when a global role is deleted.
    // The `role_id` (the string identifier, e.g., "my_translator") is needed for this.
    // However, we don't have it directly here after deletion. This needs careful handling.
    // For now, we'll just delete the role entry. A separate process or trigger might be needed for cleanup,
    // or the client should handle re-fetching related data.
    // A better approach might be to fetch the role_id BEFORE deleting, then perform cascading unassignments.

    // TODO (Challenging): Implement cascading unassignment of this role from all api_key_custom_config_roles records for this user.
    // This might involve fetching all custom_api_configs for the user, then for each config, finding api_keys, 
    // and then removing the role_name from the key_config_roles table if it matches the deleted role's role_id.
    // This is complex and potentially slow if done in a single request handler.
    // Database triggers or a background job might be more suitable for such cleanup.

    return NextResponse.json({ message: 'Custom role deleted successfully. Associated API key assignments may need to be manually reviewed or will be handled by routing logic gracefullly.' }, { status: 200 });

  } catch (e: Error) {
    console.error('Error in DELETE /api/user/custom-roles/[customRoleId]:', e);
    return NextResponse.json({ error: 'An unexpected server error occurred.', details: e.message }, { status: 500 });
  }
} 