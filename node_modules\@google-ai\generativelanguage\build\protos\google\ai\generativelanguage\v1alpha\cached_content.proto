// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ai.generativelanguage.v1alpha;

import "google/ai/generativelanguage/v1alpha/content.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/ai/generativelanguage/apiv1alpha/generativelanguagepb;generativelanguagepb";
option java_multiple_files = true;
option java_outer_classname = "CachedContentProto";
option java_package = "com.google.ai.generativelanguage.v1alpha";

// Content that has been preprocessed and can be used in subsequent request
// to GenerativeService.
//
// Cached content can be only used with model it was created for.
message CachedContent {
  option (google.api.resource) = {
    type: "generativelanguage.googleapis.com/CachedContent"
    pattern: "cachedContents/{id}"
    plural: "cachedContents"
    singular: "cachedContent"
  };

  // Metadata on the usage of the cached content.
  message UsageMetadata {
    // Total number of tokens that the cached content consumes.
    int32 total_token_count = 1;
  }

  // Specifies when this resource will expire.
  oneof expiration {
    // Timestamp in UTC of when this resource is considered expired.
    // This is *always* provided on output, regardless of what was sent
    // on input.
    google.protobuf.Timestamp expire_time = 9;

    // Input only. New TTL for this resource, input only.
    google.protobuf.Duration ttl = 10
        [(google.api.field_behavior) = INPUT_ONLY];
  }

  // Optional. Identifier. The resource name referring to the cached content.
  // Format: `cachedContents/{id}`
  optional string name = 1 [
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Immutable. The user-generated meaningful display name of the
  // cached content. Maximum 128 Unicode characters.
  optional string display_name = 11 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Immutable. The name of the `Model` to use for cached content
  // Format: `models/{model}`
  optional string model = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/Model"
    }
  ];

  // Optional. Input only. Immutable. Developer set system instruction.
  // Currently text only.
  optional Content system_instruction = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Optional. Input only. Immutable. The content to cache.
  repeated Content contents = 4 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Optional. Input only. Immutable. A list of `Tools` the model may use to
  // generate the next response
  repeated Tool tools = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Optional. Input only. Immutable. Tool config. This config is shared for all
  // tools.
  optional ToolConfig tool_config = 6 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Output only. Creation time of the cache entry.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the cache entry was last updated in UTC time.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Metadata on the usage of the cached content.
  UsageMetadata usage_metadata = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}
