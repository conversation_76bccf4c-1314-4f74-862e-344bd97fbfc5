// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ai.generativelanguage.v1alpha;

import "google/ai/generativelanguage/v1alpha/permission.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/ai/generativelanguage/apiv1alpha/generativelanguagepb;generativelanguagepb";
option java_multiple_files = true;
option java_outer_classname = "PermissionServiceProto";
option java_package = "com.google.ai.generativelanguage.v1alpha";

// Provides methods for managing permissions to PaLM API resources.
service PermissionService {
  option (google.api.default_host) = "generativelanguage.googleapis.com";

  // Create a permission to a specific resource.
  rpc CreatePermission(CreatePermissionRequest) returns (Permission) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=tunedModels/*}/permissions"
      body: "permission"
      additional_bindings {
        post: "/v1alpha/{parent=corpora/*}/permissions"
        body: "permission"
      }
    };
    option (google.api.method_signature) = "parent,permission";
  }

  // Gets information about a specific Permission.
  rpc GetPermission(GetPermissionRequest) returns (Permission) {
    option (google.api.http) = {
      get: "/v1alpha/{name=tunedModels/*/permissions/*}"
      additional_bindings { get: "/v1alpha/{name=corpora/*/permissions/*}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists permissions for the specific resource.
  rpc ListPermissions(ListPermissionsRequest)
      returns (ListPermissionsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=tunedModels/*}/permissions"
      additional_bindings { get: "/v1alpha/{parent=corpora/*}/permissions" }
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates the permission.
  rpc UpdatePermission(UpdatePermissionRequest) returns (Permission) {
    option (google.api.http) = {
      patch: "/v1alpha/{permission.name=tunedModels/*/permissions/*}"
      body: "permission"
      additional_bindings {
        patch: "/v1alpha/{permission.name=corpora/*/permissions/*}"
        body: "permission"
      }
    };
    option (google.api.method_signature) = "permission,update_mask";
  }

  // Deletes the permission.
  rpc DeletePermission(DeletePermissionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=tunedModels/*/permissions/*}"
      additional_bindings { delete: "/v1alpha/{name=corpora/*/permissions/*}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Transfers ownership of the tuned model.
  // This is the only way to change ownership of the tuned model.
  // The current owner will be downgraded to writer role.
  rpc TransferOwnership(TransferOwnershipRequest)
      returns (TransferOwnershipResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{name=tunedModels/*}:transferOwnership"
      body: "*"
    };
  }
}

// Request to create a `Permission`.
message CreatePermissionRequest {
  // Required. The parent resource of the `Permission`.
  // Formats:
  //    `tunedModels/{tuned_model}`
  //    `corpora/{corpus}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "generativelanguage.googleapis.com/Permission"
    }
  ];

  // Required. The permission to create.
  Permission permission = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for getting information about a specific `Permission`.
message GetPermissionRequest {
  // Required. The resource name of the permission.
  //
  // Formats:
  //    `tunedModels/{tuned_model}/permissions/{permission}`
  //    `corpora/{corpus}/permissions/{permission}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/Permission"
    }
  ];
}

// Request for listing permissions.
message ListPermissionsRequest {
  // Required. The parent resource of the permissions.
  // Formats:
  //    `tunedModels/{tuned_model}`
  //    `corpora/{corpus}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "*" }
  ];

  // Optional. The maximum number of `Permission`s to return (per page).
  // The service may return fewer permissions.
  //
  // If unspecified, at most 10 permissions will be returned.
  // This method returns at most 1000 permissions per page, even if you pass
  // larger page_size.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListPermissions` call.
  //
  // Provide the `page_token` returned by one request as an argument to the
  // next request to retrieve the next page.
  //
  // When paginating, all other parameters provided to `ListPermissions`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response from `ListPermissions` containing a paginated list of
// permissions.
message ListPermissionsResponse {
  // Returned permissions.
  repeated Permission permissions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  //
  // If this field is omitted, there are no more pages.
  string next_page_token = 2;
}

// Request to update the `Permission`.
message UpdatePermissionRequest {
  // Required. The permission to update.
  //
  // The permission's `name` field is used to identify the permission to update.
  Permission permission = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update. Accepted ones:
  //  - role (`Permission.role` field)
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request to delete the `Permission`.
message DeletePermissionRequest {
  // Required. The resource name of the permission.
  // Formats:
  //    `tunedModels/{tuned_model}/permissions/{permission}`
  //    `corpora/{corpus}/permissions/{permission}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/Permission"
    }
  ];
}

// Request to transfer the ownership of the tuned model.
message TransferOwnershipRequest {
  // Required. The resource name of the tuned model to transfer ownership.
  //
  // Format: `tunedModels/my-model-id`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "generativelanguage.googleapis.com/Permission"
    }
  ];

  // Required. The email address of the user to whom the tuned model is being
  // transferred to.
  string email_address = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response from `TransferOwnership`.
message TransferOwnershipResponse {}
