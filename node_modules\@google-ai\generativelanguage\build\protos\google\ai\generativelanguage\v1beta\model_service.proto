// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ai.generativelanguage.v1beta;

import "google/ai/generativelanguage/v1beta/model.proto";
import "google/ai/generativelanguage/v1beta/tuned_model.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/ai/generativelanguage/apiv1beta/generativelanguagepb;generativelanguagepb";
option java_multiple_files = true;
option java_outer_classname = "ModelServiceProto";
option java_package = "com.google.ai.generativelanguage.v1beta";

// Provides methods for getting metadata information about Generative Models.
service ModelService {
  option (google.api.default_host) = "generativelanguage.googleapis.com";

  // Gets information about a specific `Model` such as its version number, token
  // limits,
  // [parameters](https://ai.google.dev/gemini-api/docs/models/generative-models#model-parameters)
  // and other metadata. Refer to the [Gemini models
  // guide](https://ai.google.dev/gemini-api/docs/models/gemini) for detailed
  // model information.
  rpc GetModel(GetModelRequest) returns (Model) {
    option (google.api.http) = {
      get: "/v1beta/{name=models/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the [`Model`s](https://ai.google.dev/gemini-api/docs/models/gemini)
  // available through the Gemini API.
  rpc ListModels(ListModelsRequest) returns (ListModelsResponse) {
    option (google.api.http) = {
      get: "/v1beta/models"
    };
    option (google.api.method_signature) = "page_size,page_token";
  }

  // Gets information about a specific TunedModel.
  rpc GetTunedModel(GetTunedModelRequest) returns (TunedModel) {
    option (google.api.http) = {
      get: "/v1beta/{name=tunedModels/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists created tuned models.
  rpc ListTunedModels(ListTunedModelsRequest)
      returns (ListTunedModelsResponse) {
    option (google.api.http) = {
      get: "/v1beta/tunedModels"
    };
    option (google.api.method_signature) = "page_size,page_token";
  }

  // Creates a tuned model.
  // Check intermediate tuning progress (if any) through the
  // [google.longrunning.Operations] service.
  //
  // Access status and results through the Operations service.
  // Example:
  //   GET /v1/tunedModels/az2mb0bpw6i/operations/000-111-222
  rpc CreateTunedModel(CreateTunedModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/tunedModels"
      body: "tuned_model"
    };
    option (google.api.method_signature) = "tuned_model";
    option (google.api.method_signature) = "tuned_model_id,tuned_model";
    option (google.longrunning.operation_info) = {
      response_type: "TunedModel"
      metadata_type: "CreateTunedModelMetadata"
    };
  }

  // Updates a tuned model.
  rpc UpdateTunedModel(UpdateTunedModelRequest) returns (TunedModel) {
    option (google.api.http) = {
      patch: "/v1beta/{tuned_model.name=tunedModels/*}"
      body: "tuned_model"
    };
    option (google.api.method_signature) = "tuned_model,update_mask";
  }

  // Deletes a tuned model.
  rpc DeleteTunedModel(DeleteTunedModelRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=tunedModels/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request for getting information about a specific Model.
message GetModelRequest {
  // Required. The resource name of the model.
  //
  // This name should match a model name returned by the `ListModels` method.
  //
  // Format: `models/{model}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/Model"
    }
  ];
}

// Request for listing all Models.
message ListModelsRequest {
  // The maximum number of `Models` to return (per page).
  //
  // If unspecified, 50 models will be returned per page.
  // This method returns at most 1000 models per page, even if you pass a larger
  // page_size.
  int32 page_size = 2;

  // A page token, received from a previous `ListModels` call.
  //
  // Provide the `page_token` returned by one request as an argument to the next
  // request to retrieve the next page.
  //
  // When paginating, all other parameters provided to `ListModels` must match
  // the call that provided the page token.
  string page_token = 3;
}

// Response from `ListModel` containing a paginated list of Models.
message ListModelsResponse {
  // The returned Models.
  repeated Model models = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  //
  // If this field is omitted, there are no more pages.
  string next_page_token = 2;
}

// Request for getting information about a specific Model.
message GetTunedModelRequest {
  // Required. The resource name of the model.
  //
  // Format: `tunedModels/my-model-id`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/TunedModel"
    }
  ];
}

// Request for listing TunedModels.
message ListTunedModelsRequest {
  // Optional. The maximum number of `TunedModels` to return (per page).
  // The service may return fewer tuned models.
  //
  // If unspecified, at most 10 tuned models will be returned.
  // This method returns at most 1000 models per page, even if you pass a larger
  // page_size.
  int32 page_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListTunedModels` call.
  //
  // Provide the `page_token` returned by one request as an argument to the next
  // request to retrieve the next page.
  //
  // When paginating, all other parameters provided to `ListTunedModels`
  // must match the call that provided the page token.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter is a full text search over the tuned model's description
  // and display name. By default, results will not include tuned models shared
  // with everyone.
  //
  // Additional operators:
  //   - owner:me
  //   - writers:me
  //   - readers:me
  //   - readers:everyone
  //
  // Examples:
  //   "owner:me" returns all tuned models to which caller has owner role
  //   "readers:me" returns all tuned models to which caller has reader role
  //   "readers:everyone" returns all tuned models that are shared with everyone
  string filter = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response from `ListTunedModels` containing a paginated list of Models.
message ListTunedModelsResponse {
  // The returned Models.
  repeated TunedModel tuned_models = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  //
  // If this field is omitted, there are no more pages.
  string next_page_token = 2;
}

// Request to create a TunedModel.
message CreateTunedModelRequest {
  // Optional. The unique id for the tuned model if specified.
  // This value should be up to 40 characters, the first character must be a
  // letter, the last could be a letter or a number. The id must match the
  // regular expression: `[a-z]([a-z0-9-]{0,38}[a-z0-9])?`.
  optional string tuned_model_id = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The tuned model to create.
  TunedModel tuned_model = 2 [(google.api.field_behavior) = REQUIRED];
}

// Metadata about the state and progress of creating a tuned model returned from
// the long-running operation
message CreateTunedModelMetadata {
  // Name of the tuned model associated with the tuning operation.
  string tuned_model = 5 [(google.api.resource_reference) = {
    type: "generativelanguage.googleapis.com/TunedModel"
  }];

  // The total number of tuning steps.
  int32 total_steps = 1;

  // The number of steps completed.
  int32 completed_steps = 2;

  // The completed percentage for the tuning operation.
  float completed_percent = 3;

  // Metrics collected during tuning.
  repeated TuningSnapshot snapshots = 4;
}

// Request to update a TunedModel.
message UpdateTunedModelRequest {
  // Required. The tuned model to update.
  TunedModel tuned_model = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Request to delete a TunedModel.
message DeleteTunedModelRequest {
  // Required. The resource name of the model.
  // Format: `tunedModels/my-model-id`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/TunedModel"
    }
  ];
}
