import type * as gax from 'google-gax';
import type { Callback, CallOptions, Descriptors, ClientOptions, PaginationCallback } from 'google-gax';
import { Transform } from 'stream';
import * as protos from '../../protos/protos';
/**
 *  An API for uploading and managing files.
 * @class
 * @memberof v1alpha
 */
export declare class FileServiceClient {
    private _terminated;
    private _opts;
    private _providedCustomServicePath;
    private _gaxModule;
    private _gaxGrpc;
    private _protos;
    private _defaults;
    private _universeDomain;
    private _servicePath;
    private _log;
    auth: gax.GoogleAuth;
    descriptors: Descriptors;
    warn: (code: string, message: string, warnType?: string) => void;
    innerApiCalls: {
        [name: string]: Function;
    };
    pathTemplates: {
        [name: string]: gax.PathTemplate;
    };
    fileServiceStub?: Promise<{
        [name: string]: Function;
    }>;
    /**
     * Construct an instance of FileServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new FileServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts?: ClientOptions, gaxInstance?: typeof gax | typeof gax.fallback);
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize(): Promise<{
        [name: string]: Function;
    }>;
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath(): string;
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint(): string;
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint(): string;
    get universeDomain(): string;
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port(): number;
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes(): never[];
    getProjectId(): Promise<string>;
    getProjectId(callback: Callback<string, undefined, undefined>): void;
    /**
     * Creates a `File`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.ai.generativelanguage.v1alpha.File} [request.file]
     *   Optional. Metadata for the file to create.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1alpha.CreateFileResponse|CreateFileResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/file_service.create_file.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_FileService_CreateFile_async
     */
    createFile(request?: protos.google.ai.generativelanguage.v1alpha.ICreateFileRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1alpha.ICreateFileResponse,
        (protos.google.ai.generativelanguage.v1alpha.ICreateFileRequest | undefined),
        {} | undefined
    ]>;
    createFile(request: protos.google.ai.generativelanguage.v1alpha.ICreateFileRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1alpha.ICreateFileResponse, protos.google.ai.generativelanguage.v1alpha.ICreateFileRequest | null | undefined, {} | null | undefined>): void;
    createFile(request: protos.google.ai.generativelanguage.v1alpha.ICreateFileRequest, callback: Callback<protos.google.ai.generativelanguage.v1alpha.ICreateFileResponse, protos.google.ai.generativelanguage.v1alpha.ICreateFileRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets the metadata for the given `File`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the `File` to get.
     *   Example: `files/abc-123`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1alpha.File|File}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/file_service.get_file.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_FileService_GetFile_async
     */
    getFile(request?: protos.google.ai.generativelanguage.v1alpha.IGetFileRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1alpha.IFile,
        protos.google.ai.generativelanguage.v1alpha.IGetFileRequest | undefined,
        {} | undefined
    ]>;
    getFile(request: protos.google.ai.generativelanguage.v1alpha.IGetFileRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1alpha.IFile, protos.google.ai.generativelanguage.v1alpha.IGetFileRequest | null | undefined, {} | null | undefined>): void;
    getFile(request: protos.google.ai.generativelanguage.v1alpha.IGetFileRequest, callback: Callback<protos.google.ai.generativelanguage.v1alpha.IFile, protos.google.ai.generativelanguage.v1alpha.IGetFileRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Deletes the `File`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the `File` to delete.
     *   Example: `files/abc-123`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/file_service.delete_file.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_FileService_DeleteFile_async
     */
    deleteFile(request?: protos.google.ai.generativelanguage.v1alpha.IDeleteFileRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        (protos.google.ai.generativelanguage.v1alpha.IDeleteFileRequest | undefined),
        {} | undefined
    ]>;
    deleteFile(request: protos.google.ai.generativelanguage.v1alpha.IDeleteFileRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1alpha.IDeleteFileRequest | null | undefined, {} | null | undefined>): void;
    deleteFile(request: protos.google.ai.generativelanguage.v1alpha.IDeleteFileRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1alpha.IDeleteFileRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Lists the metadata for `File`s owned by the requesting project.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. Maximum number of `File`s to return per page.
     *   If unspecified, defaults to 10. Maximum `page_size` is 100.
     * @param {string} [request.pageToken]
     *   Optional. A page token from a previous `ListFiles` call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.ai.generativelanguage.v1alpha.File|File}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listFilesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listFiles(request?: protos.google.ai.generativelanguage.v1alpha.IListFilesRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1alpha.IFile[],
        protos.google.ai.generativelanguage.v1alpha.IListFilesRequest | null,
        protos.google.ai.generativelanguage.v1alpha.IListFilesResponse
    ]>;
    listFiles(request: protos.google.ai.generativelanguage.v1alpha.IListFilesRequest, options: CallOptions, callback: PaginationCallback<protos.google.ai.generativelanguage.v1alpha.IListFilesRequest, protos.google.ai.generativelanguage.v1alpha.IListFilesResponse | null | undefined, protos.google.ai.generativelanguage.v1alpha.IFile>): void;
    listFiles(request: protos.google.ai.generativelanguage.v1alpha.IListFilesRequest, callback: PaginationCallback<protos.google.ai.generativelanguage.v1alpha.IListFilesRequest, protos.google.ai.generativelanguage.v1alpha.IListFilesResponse | null | undefined, protos.google.ai.generativelanguage.v1alpha.IFile>): void;
    /**
     * Equivalent to `listFiles`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. Maximum number of `File`s to return per page.
     *   If unspecified, defaults to 10. Maximum `page_size` is 100.
     * @param {string} [request.pageToken]
     *   Optional. A page token from a previous `ListFiles` call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1alpha.File|File} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listFilesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listFilesStream(request?: protos.google.ai.generativelanguage.v1alpha.IListFilesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listFiles`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. Maximum number of `File`s to return per page.
     *   If unspecified, defaults to 10. Maximum `page_size` is 100.
     * @param {string} [request.pageToken]
     *   Optional. A page token from a previous `ListFiles` call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1alpha.File|File}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1alpha/file_service.list_files.js</caption>
     * region_tag:generativelanguage_v1alpha_generated_FileService_ListFiles_async
     */
    listFilesAsync(request?: protos.google.ai.generativelanguage.v1alpha.IListFilesRequest, options?: CallOptions): AsyncIterable<protos.google.ai.generativelanguage.v1alpha.IFile>;
    /**
     * Return a fully-qualified cachedContent resource name string.
     *
     * @param {string} id
     * @returns {string} Resource name string.
     */
    cachedContentPath(id: string): string;
    /**
     * Parse the id from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the id.
     */
    matchIdFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Return a fully-qualified chunk resource name string.
     *
     * @param {string} corpus
     * @param {string} document
     * @param {string} chunk
     * @returns {string} Resource name string.
     */
    chunkPath(corpus: string, document: string, chunk: string): string;
    /**
     * Parse the corpus from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromChunkName(chunkName: string): string | number;
    /**
     * Parse the document from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromChunkName(chunkName: string): string | number;
    /**
     * Parse the chunk from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the chunk.
     */
    matchChunkFromChunkName(chunkName: string): string | number;
    /**
     * Return a fully-qualified corpus resource name string.
     *
     * @param {string} corpus
     * @returns {string} Resource name string.
     */
    corpusPath(corpus: string): string;
    /**
     * Parse the corpus from Corpus resource.
     *
     * @param {string} corpusName
     *   A fully-qualified path representing Corpus resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromCorpusName(corpusName: string): string | number;
    /**
     * Return a fully-qualified corpusPermission resource name string.
     *
     * @param {string} corpus
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    corpusPermissionPath(corpus: string, permission: string): string;
    /**
     * Parse the corpus from CorpusPermission resource.
     *
     * @param {string} corpusPermissionName
     *   A fully-qualified path representing corpus_permission resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromCorpusPermissionName(corpusPermissionName: string): string | number;
    /**
     * Parse the permission from CorpusPermission resource.
     *
     * @param {string} corpusPermissionName
     *   A fully-qualified path representing corpus_permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromCorpusPermissionName(corpusPermissionName: string): string | number;
    /**
     * Return a fully-qualified document resource name string.
     *
     * @param {string} corpus
     * @param {string} document
     * @returns {string} Resource name string.
     */
    documentPath(corpus: string, document: string): string;
    /**
     * Parse the corpus from Document resource.
     *
     * @param {string} documentName
     *   A fully-qualified path representing Document resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromDocumentName(documentName: string): string | number;
    /**
     * Parse the document from Document resource.
     *
     * @param {string} documentName
     *   A fully-qualified path representing Document resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromDocumentName(documentName: string): string | number;
    /**
     * Return a fully-qualified file resource name string.
     *
     * @param {string} file
     * @returns {string} Resource name string.
     */
    filePath(file: string): string;
    /**
     * Parse the file from File resource.
     *
     * @param {string} fileName
     *   A fully-qualified path representing File resource.
     * @returns {string} A string representing the file.
     */
    matchFileFromFileName(fileName: string): string | number;
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(model: string): string;
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName: string): string | number;
    /**
     * Return a fully-qualified tunedModel resource name string.
     *
     * @param {string} tuned_model
     * @returns {string} Resource name string.
     */
    tunedModelPath(tunedModel: string): string;
    /**
     * Parse the tuned_model from TunedModel resource.
     *
     * @param {string} tunedModelName
     *   A fully-qualified path representing TunedModel resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelName(tunedModelName: string): string | number;
    /**
     * Return a fully-qualified tunedModelPermission resource name string.
     *
     * @param {string} tuned_model
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    tunedModelPermissionPath(tunedModel: string, permission: string): string;
    /**
     * Parse the tuned_model from TunedModelPermission resource.
     *
     * @param {string} tunedModelPermissionName
     *   A fully-qualified path representing tuned_model_permission resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelPermissionName(tunedModelPermissionName: string): string | number;
    /**
     * Parse the permission from TunedModelPermission resource.
     *
     * @param {string} tunedModelPermissionName
     *   A fully-qualified path representing tuned_model_permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromTunedModelPermissionName(tunedModelPermissionName: string): string | number;
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close(): Promise<void>;
}
