// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ai.generativelanguage.v1beta;

import "google/ai/generativelanguage/v1beta/file.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";

option go_package = "cloud.google.com/go/ai/generativelanguage/apiv1beta/generativelanguagepb;generativelanguagepb";
option java_multiple_files = true;
option java_outer_classname = "FileServiceProto";
option java_package = "com.google.ai.generativelanguage.v1beta";

// An API for uploading and managing files.
service FileService {
  option (google.api.default_host) = "generativelanguage.googleapis.com";

  // Creates a `File`.
  rpc CreateFile(CreateFileRequest) returns (CreateFileResponse) {
    option (google.api.http) = {
      post: "/v1beta/files"
      body: "*"
    };
  }

  // Lists the metadata for `File`s owned by the requesting project.
  rpc ListFiles(ListFilesRequest) returns (ListFilesResponse) {
    option (google.api.http) = {
      get: "/v1beta/files"
    };
  }

  // Gets the metadata for the given `File`.
  rpc GetFile(GetFileRequest) returns (File) {
    option (google.api.http) = {
      get: "/v1beta/{name=files/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes the `File`.
  rpc DeleteFile(DeleteFileRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=files/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Download the `File`.
  rpc DownloadFile(DownloadFileRequest) returns (DownloadFileResponse) {
    option (google.api.http) = {
      get: "/v1beta/{name=files/*}:download"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request for `CreateFile`.
message CreateFileRequest {
  // Optional. Metadata for the file to create.
  File file = 1 [(google.api.field_behavior) = OPTIONAL];
}

// Response for `CreateFile`.
message CreateFileResponse {
  // Metadata for the created file.
  File file = 1;
}

// Request for `ListFiles`.
message ListFilesRequest {
  // Optional. Maximum number of `File`s to return per page.
  // If unspecified, defaults to 10. Maximum `page_size` is 100.
  int32 page_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token from a previous `ListFiles` call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response for `ListFiles`.
message ListFilesResponse {
  // The list of `File`s.
  repeated File files = 1;

  // A token that can be sent as a `page_token` into a subsequent `ListFiles`
  // call.
  string next_page_token = 2;
}

// Request for `GetFile`.
message GetFileRequest {
  // Required. The name of the `File` to get.
  // Example: `files/abc-123`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/File"
    }
  ];
}

// Request for `DeleteFile`.
message DeleteFileRequest {
  // Required. The name of the `File` to delete.
  // Example: `files/abc-123`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/File"
    }
  ];
}

// Request for `DownloadFile`.
message DownloadFileRequest {
  // Required. The name of the `File` to download.
  // Example: `files/abc-123`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/File"
    }
  ];
}

// Response for `DownloadFile`.
message DownloadFileResponse {}
