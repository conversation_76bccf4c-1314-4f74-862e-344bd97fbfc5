{"schema": "1.0", "comment": "This file maps proto services/RPCs to the corresponding library clients/methods", "language": "typescript", "protoPackage": "google.ai.generativelanguage.v1", "libraryPackage": "@google-ai/generativelanguage", "services": {"GenerativeService": {"clients": {"grpc": {"libraryClient": "GenerativeServiceClient", "rpcs": {"GenerateContent": {"methods": ["generateContent"]}, "EmbedContent": {"methods": ["embedContent"]}, "BatchEmbedContents": {"methods": ["batchEmbedContents"]}, "CountTokens": {"methods": ["countTokens"]}, "StreamGenerateContent": {"methods": ["streamGenerateContent"]}}}, "grpc-fallback": {"libraryClient": "GenerativeServiceClient", "rpcs": {"GenerateContent": {"methods": ["generateContent"]}, "EmbedContent": {"methods": ["embedContent"]}, "BatchEmbedContents": {"methods": ["batchEmbedContents"]}, "CountTokens": {"methods": ["countTokens"]}}}}}, "ModelService": {"clients": {"grpc": {"libraryClient": "ModelServiceClient", "rpcs": {"GetModel": {"methods": ["getModel"]}, "ListModels": {"methods": ["listModels", "listModelsStream", "listModelsAsync"]}}}, "grpc-fallback": {"libraryClient": "ModelServiceClient", "rpcs": {"GetModel": {"methods": ["getModel"]}, "ListModels": {"methods": ["listModels", "listModelsStream", "listModelsAsync"]}}}}}}}