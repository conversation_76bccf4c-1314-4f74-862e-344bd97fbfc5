'use client';

import React, { useState, useEffect } from 'react';
import { useOrchestrationStream } from '@/hooks/useOrchestrationStream';
import { OrchestrationChatroom } from './OrchestrationChatroom';
import {
  MinusIcon,
  ChatBubbleLeftRightIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

interface OrchestrationCanvasProps {
  executionId: string;
  onComplete?: (result: string) => void;
  onError?: (error: string) => void;
  onCanvasStateChange?: (isOpen: boolean, isMinimized: boolean) => void;
  forceMaximize?: boolean; // External trigger to maximize
}

export const OrchestrationCanvas: React.FC<OrchestrationCanvasProps> = ({
  executionId,
  onComplete,
  onError,
  onCanvasStateChange,
  forceMaximize = false
}) => {
  const [isCanvasOpen, setIsCanvasOpen] = useState(true);
  const [isMinimized, setIsMinimized] = useState(false);
  const [orchestrationComplete, setOrchestrationComplete] = useState(false);
  const [finalResult, setFinalResult] = useState<string>('');

  const { events, isConnected, error } = useOrchestrationStream(executionId);

  // Handle orchestration completion
  useEffect(() => {
    const synthesisCompleteEvent = events.find(event => event.type === 'synthesis_complete');
    if (synthesisCompleteEvent && !orchestrationComplete) {
      setOrchestrationComplete(true);
      const result = synthesisCompleteEvent.data?.result || 'Orchestration completed successfully';
      setFinalResult(result);
      
      // Notify parent component
      if (onComplete) {
        onComplete(result);
      }
    }
  }, [events, orchestrationComplete, onComplete]);

  // Handle errors
  useEffect(() => {
    if (error && onError) {
      onError(error);
    }
  }, [error, onError]);

  const handleMinimize = () => {
    setIsMinimized(true);
    // Keep isCanvasOpen as true so component doesn't disappear completely
    // We'll hide it via CSS transform instead
    onCanvasStateChange?.(false, true);
  };

  const handleMaximize = () => {
    setIsMinimized(false);
    // isCanvasOpen should already be true, but ensure it
    setIsCanvasOpen(true);
    onCanvasStateChange?.(true, false);
  };



  // Notify parent of initial canvas state
  useEffect(() => {
    onCanvasStateChange?.(isCanvasOpen, isMinimized);
  }, [isCanvasOpen, isMinimized, onCanvasStateChange]);

  // Handle external maximize trigger
  useEffect(() => {
    if (forceMaximize && isMinimized) {
      console.log('🎭 [DEBUG] External maximize trigger received!');
      handleMaximize();
    }
  }, [forceMaximize, isMinimized, handleMaximize]);

  // Minimized card state - now returns null, will be rendered inline in chat
  if (isMinimized) {
    return null;
  }

  // Canvas is closed
  if (!isCanvasOpen) {
    return null;
  }

  // Debug log when rendering
  console.log('🎭 [DEBUG] OrchestrationCanvas is rendering!', {
    isCanvasOpen,
    isMinimized,
    executionId,
    shouldBeVisible: isCanvasOpen && !isMinimized,
    transformClass: (isCanvasOpen && !isMinimized) ? 'translate-x-0' : 'translate-x-full'
  });

  return (
    <>
      {/* Canvas Panel - Premium Split Screen Layout */}
      <div className={`fixed top-0 right-0 h-full w-1/2 bg-gradient-to-br from-gray-900 via-black to-gray-900 shadow-2xl z-[9999] transform transition-all duration-500 ease-out border-l border-orange-500/20 ${
        isCanvasOpen && !isMinimized ? 'translate-x-0' : 'translate-x-full'
      }`}>
        {/* Glowing border effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 via-transparent to-orange-500/10 pointer-events-none" />
        <div className="absolute left-0 top-0 h-full w-[2px] bg-gradient-to-b from-transparent via-orange-500 to-transparent animate-pulse" />

        {/* Canvas Header - Premium Dark Design */}
        <div className="relative flex items-center justify-between p-6 border-b border-orange-500/20 bg-gradient-to-r from-black/80 via-gray-900/90 to-black/80 backdrop-blur-sm">
          {/* Glowing header accent */}
          <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-orange-500/50 to-transparent" />

          <div className="flex items-center space-x-4">
            {/* Premium icon with glow effect */}
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                <ChatBubbleLeftRightIcon className="w-5 h-5 text-white" />
              </div>
              <div className="absolute inset-0 bg-orange-500/30 rounded-xl blur-md -z-10 animate-pulse" />
            </div>

            <div>
              <h2 className="font-bold text-white text-lg tracking-wide">AI Team Collaboration</h2>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${orchestrationComplete ? 'bg-green-400' : 'bg-orange-400'} animate-pulse`} />
                <p className="text-sm text-gray-300 font-medium">
                  {orchestrationComplete ? 'Mission Complete' : 'Team Active'}
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Status indicator */}
            <div className="flex items-center space-x-2 px-3 py-1.5 bg-orange-500/10 border border-orange-500/20 rounded-full">
              <SparklesIcon className="w-4 h-4 text-orange-400" />
              <span className="text-xs text-orange-300 font-medium">LIVE</span>
            </div>

            {/* Minimize button with hover glow */}
            <button
              onClick={handleMinimize}
              className="group relative p-2.5 text-gray-400 hover:text-white hover:bg-orange-500/20 rounded-xl transition-all duration-300 border border-transparent hover:border-orange-500/30"
              aria-label="Minimize canvas"
            >
              <MinusIcon className="w-5 h-5 transition-transform group-hover:scale-110" />
              <div className="absolute inset-0 bg-orange-500/20 rounded-xl opacity-0 group-hover:opacity-100 blur-sm transition-opacity duration-300 -z-10" />
            </button>
          </div>
        </div>

        {/* Canvas Content with enhanced styling */}
        <div className="flex-1 h-full overflow-hidden relative">
          {/* Subtle background pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,107,53,0.1),transparent_50%)]" />
            <div className="absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,107,53,0.05)_50%,transparent_100%)]" />
          </div>

          <OrchestrationChatroom
            executionId={executionId}
            events={events}
            isConnected={isConnected}
            error={error}
            isComplete={orchestrationComplete}
          />
        </div>
      </div>
    </>
  );
};
