'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  ChartBarIcon,
  KeyIcon,
  BeakerIcon,
  DocumentTextIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
  CpuChipIcon,
  GlobeAltIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  PlusIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface AnalyticsSummary {
  total_requests: number;
  successful_requests: number;
  success_rate: number;
  total_cost: number;
  total_input_tokens: number;
  total_output_tokens: number;
  total_tokens: number;
  average_cost_per_request: number;
}

interface AnalyticsData {
  summary: AnalyticsSummary;
  grouped_data: any[];
  filters: any;
}

interface RecentActivityItem {
  id: string;
  action: string;
  model: string;
  time: string;
  status: 'success' | 'warning' | 'error' | 'info';
  details?: string;
}

interface SystemStatusItem {
  name: string;
  status: 'operational' | 'degraded' | 'down';
  lastChecked?: string;
}

export default function DashboardPage() {
  const router = useRouter();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(false); // Start with false for progressive loading
  const [initialLoad, setInitialLoad] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivityItem[]>([]);
  const [systemStatus, setSystemStatus] = useState<SystemStatusItem[]>([
    { name: 'API Gateway', status: 'operational' },
    { name: 'Routing Engine', status: 'operational' },
    { name: 'Analytics', status: 'degraded' }
  ]);

  useEffect(() => {
    // Progressive loading: render UI first, then load data
    const loadData = async () => {
      // Small delay to allow UI to render first
      await new Promise(resolve => setTimeout(resolve, 50));

      // Load data in parallel for better performance
      const promises = [
        fetchAnalyticsData(),
        fetchRecentActivity(),
        checkSystemStatus()
      ];

      await Promise.allSettled(promises);
      setInitialLoad(false);
    };

    loadData();

    // Set up auto-refresh for activity feed every 30 seconds
    const activityInterval = setInterval(fetchRecentActivity, 30000);

    // Set up system status check every 60 seconds
    const statusInterval = setInterval(checkSystemStatus, 60000);

    return () => {
      clearInterval(activityInterval);
      clearInterval(statusInterval);
    };
  }, []);

  const fetchAnalyticsData = useCallback(async () => {
    try {
      // Only show loading on initial load, not on refreshes
      if (initialLoad) {
        setLoading(true);
      }

      // Get data for the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const response = await fetch(`/api/analytics/summary?startDate=${thirtyDaysAgo.toISOString()}&groupBy=day`);
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const data = await response.json();
      setAnalyticsData(data);
    } catch (err: any) {
      setError(err.message);
      console.error('Error fetching analytics:', err);
    } finally {
      if (initialLoad) {
        setLoading(false);
      }
    }
  }, [initialLoad]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const fetchRecentActivity = async () => {
    try {
      // Fetch recent activity from the new activity API
      const response = await fetch('/api/activity?limit=10');
      if (!response.ok) {
        throw new Error('Failed to fetch recent activity');
      }

      const data = await response.json();
      const activities: RecentActivityItem[] = data.activities.map((activity: any) => ({
        id: activity.id,
        action: activity.action,
        model: activity.model,
        time: activity.time,
        status: activity.status,
        details: activity.details
      }));

      setRecentActivity(activities);
    } catch (err: any) {
      console.error('Error fetching recent activity:', err);
      // Set fallback activity data
      setRecentActivity([
        { id: '1', action: 'System initialized', model: 'RoKey', time: 'Just now', status: 'info' }
      ]);
    }
  };

  const checkSystemStatus = async () => {
    try {
      const response = await fetch('/api/system-status');
      if (!response.ok) {
        throw new Error('Failed to fetch system status');
      }

      const data = await response.json();
      const statusItems: SystemStatusItem[] = data.checks.map((check: any) => ({
        name: check.name,
        status: check.status,
        lastChecked: new Date(check.lastChecked).toLocaleTimeString()
      }));

      setSystemStatus(statusItems);
    } catch (err) {
      console.error('Error checking system status:', err);
      // Set fallback status
      setSystemStatus([
        { name: 'API Gateway', status: 'down', lastChecked: new Date().toLocaleTimeString() },
        { name: 'Routing Engine', status: 'down', lastChecked: new Date().toLocaleTimeString() },
        { name: 'Analytics', status: 'down', lastChecked: new Date().toLocaleTimeString() }
      ]);
    }
  };

  const getTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  // Quick Actions handlers
  const handleAddNewModel = () => {
    router.push('/my-models');
  };

  const handleTestPlayground = () => {
    router.push('/playground');
  };

  const handleViewLogs = () => {
    router.push('/logs');
  };

  const stats = analyticsData ? [
    {
      name: 'Total Requests',
      value: formatNumber(analyticsData.summary.total_requests),
      change: 'Last 30 days',
      changeType: 'neutral',
      icon: ChartBarIcon,
    },
    {
      name: 'Total Cost',
      value: formatCurrency(analyticsData.summary.total_cost),
      change: `${formatCurrency(analyticsData.summary.average_cost_per_request)} avg/request`,
      changeType: 'neutral',
      icon: CurrencyDollarIcon,
    },
    {
      name: 'Success Rate',
      value: `${analyticsData.summary.success_rate.toFixed(1)}%`,
      change: `${formatNumber(analyticsData.summary.successful_requests)} successful`,
      changeType: analyticsData.summary.success_rate >= 95 ? 'positive' : 'negative',
      icon: CheckCircleIcon,
    },
    {
      name: 'Total Tokens',
      value: formatNumber(analyticsData.summary.total_tokens),
      change: `${formatNumber(analyticsData.summary.total_input_tokens)} in, ${formatNumber(analyticsData.summary.total_output_tokens)} out`,
      changeType: 'neutral',
      icon: CpuChipIcon,
    },
  ] : [];

  // Show loading only on initial load, not on subsequent visits
  if (loading && initialLoad) {
    return (
      <div className="space-y-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="animate-slide-in">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Welcome back! 👋
          </h1>
          <p className="text-gray-600 text-lg">
            Here's what's happening with your LLM infrastructure today.
          </p>
        </div>
        <div className="card p-6 text-center">
          <p className="text-red-600 mb-4">Error loading analytics data: {error}</p>
          <button
            onClick={fetchAnalyticsData}
            className="btn-primary"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="animate-slide-in">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">
          Welcome back! 👋
        </h1>
        <p className="text-gray-600 text-lg">
          Here's what's happening with your LLM infrastructure today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-slide-in">
        {stats.map((stat, index) => (
          <div
            key={stat.name}
            className="card p-6 hover:shadow-lg transition-all duration-200"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
                <p className={`text-sm mt-2 flex items-center ${
                  stat.changeType === 'positive' ? 'text-green-600' :
                  stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {stat.changeType !== 'neutral' && (
                    <ArrowTrendingUpIcon className={`h-4 w-4 mr-1 ${
                      stat.changeType === 'negative' ? 'rotate-180' : ''
                    }`} />
                  )}
                  {stat.change}
                </p>
              </div>
              <div className="p-3 rounded-lg bg-orange-50">
                <stat.icon className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Quick Actions */}
        <div className="lg:col-span-1 space-y-6">
          <div className="card p-6 animate-slide-in">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <button
                onClick={handleAddNewModel}
                className="btn-primary w-full justify-center hover:scale-105 transition-transform duration-200"
              >
                <PlusIcon className="h-5 w-5 mr-3" />
                Add New Model
              </button>
              <button
                onClick={handleTestPlayground}
                className="btn-secondary w-full justify-center hover:scale-105 transition-transform duration-200"
              >
                <BeakerIcon className="h-5 w-5 mr-3" />
                Test in Playground
              </button>
              <button
                onClick={handleViewLogs}
                className="btn-outline w-full justify-center hover:scale-105 transition-transform duration-200"
              >
                <DocumentTextIcon className="h-5 w-5 mr-3" />
                View Logs
              </button>
            </div>
          </div>

          {/* System Status */}
          <div className="card p-6 animate-slide-in">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">System Status</h3>
            <div className="space-y-4">
              {systemStatus.map((system) => (
                <div key={system.name} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${
                      system.status === 'operational' ? 'bg-green-500' :
                      system.status === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
                    }`}></div>
                    <span className="text-gray-700">{system.name}</span>
                  </div>
                  <div className="text-right">
                    <span className={`text-sm font-medium ${
                      system.status === 'operational' ? 'text-green-600' :
                      system.status === 'degraded' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {system.status === 'operational' ? 'Operational' :
                       system.status === 'degraded' ? 'Degraded' : 'Down'}
                    </span>
                    {system.lastChecked && (
                      <p className="text-xs text-gray-500">{system.lastChecked}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <div className="card p-6 animate-slide-in">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Recent Activity</h3>
              <button
                onClick={fetchRecentActivity}
                className="text-orange-600 hover:text-orange-700 text-sm font-medium"
              >
                Refresh
              </button>
            </div>
            <div className="space-y-4">
              {recentActivity.length === 0 ? (
                <div className="text-center py-8">
                  <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No recent activity</p>
                  <p className="text-gray-400 text-sm">Activity will appear here as you use the API</p>
                </div>
              ) : (
                recentActivity.slice(-4).map((activity) => (
                  <div key={activity.id} className="flex items-start p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-200 group overflow-hidden">
                    <div className={`w-3 h-3 rounded-full mr-4 ${
                      activity.status === 'success' ? 'bg-green-500' :
                      activity.status === 'warning' ? 'bg-yellow-500' :
                      activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                    }`}></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-gray-900 font-medium break-words">{activity.action}</p>
                      <p className="text-gray-600 text-sm break-words">{activity.model} • {activity.time}</p>
                      {activity.details && (
                        <p className="text-gray-500 text-xs mt-1 line-clamp-2 leading-relaxed" title={activity.details}>
                          {activity.details}
                        </p>
                      )}
                    </div>
                    <div className="text-gray-500 group-hover:text-gray-700">
                      {activity.status === 'error' ? (
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                      ) : (
                        <CpuChipIcon className="h-5 w-5" />
                      )}
                    </div>
                  </div>
                ))
              )}

              {/* View All Link - only show if there are more than 4 items */}
              {recentActivity.length > 4 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => {
                      // Navigate to logs page to see all activity
                      window.location.href = '/logs';
                    }}
                    className="text-sm text-orange-600 hover:text-orange-700 font-medium flex items-center justify-center w-full py-2 hover:bg-orange-50 rounded-lg transition-colors"
                  >
                    View All Activity ({recentActivity.length})
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}