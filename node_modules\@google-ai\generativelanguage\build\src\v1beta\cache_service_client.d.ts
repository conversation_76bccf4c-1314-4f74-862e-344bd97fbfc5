import type * as gax from 'google-gax';
import type { Callback, CallOptions, Descriptors, ClientOptions, PaginationCallback } from 'google-gax';
import { Transform } from 'stream';
import * as protos from '../../protos/protos';
/**
 *  API for managing cache of content (CachedContent resources) that can be used
 *  in GenerativeService requests. This way generate content requests can benefit
 *  from preprocessing work being done earlier, possibly lowering their
 *  computational cost. It is intended to be used with large contexts.
 * @class
 * @memberof v1beta
 */
export declare class CacheServiceClient {
    private _terminated;
    private _opts;
    private _providedCustomServicePath;
    private _gaxModule;
    private _gaxGrpc;
    private _protos;
    private _defaults;
    private _universeDomain;
    private _servicePath;
    private _log;
    auth: gax.GoogleAuth;
    descriptors: Descriptors;
    warn: (code: string, message: string, warnType?: string) => void;
    innerApiCalls: {
        [name: string]: Function;
    };
    pathTemplates: {
        [name: string]: gax.PathTemplate;
    };
    cacheServiceStub?: Promise<{
        [name: string]: Function;
    }>;
    /**
     * Construct an instance of CacheServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new CacheServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts?: ClientOptions, gaxInstance?: typeof gax | typeof gax.fallback);
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize(): Promise<{
        [name: string]: Function;
    }>;
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath(): string;
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint(): string;
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint(): string;
    get universeDomain(): string;
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port(): number;
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes(): never[];
    getProjectId(): Promise<string>;
    getProjectId(callback: Callback<string, undefined, undefined>): void;
    /**
     * Creates CachedContent resource.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.ai.generativelanguage.v1beta.CachedContent} request.cachedContent
     *   Required. The cached content to create.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.CachedContent|CachedContent}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/cache_service.create_cached_content.js</caption>
     * region_tag:generativelanguage_v1beta_generated_CacheService_CreateCachedContent_async
     */
    createCachedContent(request?: protos.google.ai.generativelanguage.v1beta.ICreateCachedContentRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.ICachedContent,
        (protos.google.ai.generativelanguage.v1beta.ICreateCachedContentRequest | undefined),
        {} | undefined
    ]>;
    createCachedContent(request: protos.google.ai.generativelanguage.v1beta.ICreateCachedContentRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICachedContent, protos.google.ai.generativelanguage.v1beta.ICreateCachedContentRequest | null | undefined, {} | null | undefined>): void;
    createCachedContent(request: protos.google.ai.generativelanguage.v1beta.ICreateCachedContentRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICachedContent, protos.google.ai.generativelanguage.v1beta.ICreateCachedContentRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Reads CachedContent resource.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name referring to the content cache entry.
     *   Format: `cachedContents/{id}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.CachedContent|CachedContent}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/cache_service.get_cached_content.js</caption>
     * region_tag:generativelanguage_v1beta_generated_CacheService_GetCachedContent_async
     */
    getCachedContent(request?: protos.google.ai.generativelanguage.v1beta.IGetCachedContentRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.ICachedContent,
        (protos.google.ai.generativelanguage.v1beta.IGetCachedContentRequest | undefined),
        {} | undefined
    ]>;
    getCachedContent(request: protos.google.ai.generativelanguage.v1beta.IGetCachedContentRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICachedContent, protos.google.ai.generativelanguage.v1beta.IGetCachedContentRequest | null | undefined, {} | null | undefined>): void;
    getCachedContent(request: protos.google.ai.generativelanguage.v1beta.IGetCachedContentRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICachedContent, protos.google.ai.generativelanguage.v1beta.IGetCachedContentRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates CachedContent resource (only expiration is updatable).
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.ai.generativelanguage.v1beta.CachedContent} request.cachedContent
     *   Required. The content cache entry to update
     * @param {google.protobuf.FieldMask} request.updateMask
     *   The list of fields to update.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.CachedContent|CachedContent}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/cache_service.update_cached_content.js</caption>
     * region_tag:generativelanguage_v1beta_generated_CacheService_UpdateCachedContent_async
     */
    updateCachedContent(request?: protos.google.ai.generativelanguage.v1beta.IUpdateCachedContentRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.ICachedContent,
        (protos.google.ai.generativelanguage.v1beta.IUpdateCachedContentRequest | undefined),
        {} | undefined
    ]>;
    updateCachedContent(request: protos.google.ai.generativelanguage.v1beta.IUpdateCachedContentRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICachedContent, protos.google.ai.generativelanguage.v1beta.IUpdateCachedContentRequest | null | undefined, {} | null | undefined>): void;
    updateCachedContent(request: protos.google.ai.generativelanguage.v1beta.IUpdateCachedContentRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICachedContent, protos.google.ai.generativelanguage.v1beta.IUpdateCachedContentRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Deletes CachedContent resource.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name referring to the content cache entry
     *   Format: `cachedContents/{id}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/cache_service.delete_cached_content.js</caption>
     * region_tag:generativelanguage_v1beta_generated_CacheService_DeleteCachedContent_async
     */
    deleteCachedContent(request?: protos.google.ai.generativelanguage.v1beta.IDeleteCachedContentRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        (protos.google.ai.generativelanguage.v1beta.IDeleteCachedContentRequest | undefined),
        {} | undefined
    ]>;
    deleteCachedContent(request: protos.google.ai.generativelanguage.v1beta.IDeleteCachedContentRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1beta.IDeleteCachedContentRequest | null | undefined, {} | null | undefined>): void;
    deleteCachedContent(request: protos.google.ai.generativelanguage.v1beta.IDeleteCachedContentRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1beta.IDeleteCachedContentRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Lists CachedContents.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of cached contents to return. The service may
     *   return fewer than this value. If unspecified, some default (under maximum)
     *   number of items will be returned. The maximum value is 1000; values above
     *   1000 will be coerced to 1000.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListCachedContents` call.
     *   Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to `ListCachedContents` must
     *   match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.ai.generativelanguage.v1beta.CachedContent|CachedContent}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listCachedContentsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listCachedContents(request?: protos.google.ai.generativelanguage.v1beta.IListCachedContentsRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.ICachedContent[],
        protos.google.ai.generativelanguage.v1beta.IListCachedContentsRequest | null,
        protos.google.ai.generativelanguage.v1beta.IListCachedContentsResponse
    ]>;
    listCachedContents(request: protos.google.ai.generativelanguage.v1beta.IListCachedContentsRequest, options: CallOptions, callback: PaginationCallback<protos.google.ai.generativelanguage.v1beta.IListCachedContentsRequest, protos.google.ai.generativelanguage.v1beta.IListCachedContentsResponse | null | undefined, protos.google.ai.generativelanguage.v1beta.ICachedContent>): void;
    listCachedContents(request: protos.google.ai.generativelanguage.v1beta.IListCachedContentsRequest, callback: PaginationCallback<protos.google.ai.generativelanguage.v1beta.IListCachedContentsRequest, protos.google.ai.generativelanguage.v1beta.IListCachedContentsResponse | null | undefined, protos.google.ai.generativelanguage.v1beta.ICachedContent>): void;
    /**
     * Equivalent to `listCachedContents`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of cached contents to return. The service may
     *   return fewer than this value. If unspecified, some default (under maximum)
     *   number of items will be returned. The maximum value is 1000; values above
     *   1000 will be coerced to 1000.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListCachedContents` call.
     *   Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to `ListCachedContents` must
     *   match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1beta.CachedContent|CachedContent} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listCachedContentsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listCachedContentsStream(request?: protos.google.ai.generativelanguage.v1beta.IListCachedContentsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listCachedContents`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of cached contents to return. The service may
     *   return fewer than this value. If unspecified, some default (under maximum)
     *   number of items will be returned. The maximum value is 1000; values above
     *   1000 will be coerced to 1000.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListCachedContents` call.
     *   Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to `ListCachedContents` must
     *   match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1beta.CachedContent|CachedContent}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/cache_service.list_cached_contents.js</caption>
     * region_tag:generativelanguage_v1beta_generated_CacheService_ListCachedContents_async
     */
    listCachedContentsAsync(request?: protos.google.ai.generativelanguage.v1beta.IListCachedContentsRequest, options?: CallOptions): AsyncIterable<protos.google.ai.generativelanguage.v1beta.ICachedContent>;
    /**
     * Return a fully-qualified cachedContent resource name string.
     *
     * @param {string} id
     * @returns {string} Resource name string.
     */
    cachedContentPath(id: string): string;
    /**
     * Parse the id from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the id.
     */
    matchIdFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Return a fully-qualified chunk resource name string.
     *
     * @param {string} corpus
     * @param {string} document
     * @param {string} chunk
     * @returns {string} Resource name string.
     */
    chunkPath(corpus: string, document: string, chunk: string): string;
    /**
     * Parse the corpus from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromChunkName(chunkName: string): string | number;
    /**
     * Parse the document from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromChunkName(chunkName: string): string | number;
    /**
     * Parse the chunk from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the chunk.
     */
    matchChunkFromChunkName(chunkName: string): string | number;
    /**
     * Return a fully-qualified corpus resource name string.
     *
     * @param {string} corpus
     * @returns {string} Resource name string.
     */
    corpusPath(corpus: string): string;
    /**
     * Parse the corpus from Corpus resource.
     *
     * @param {string} corpusName
     *   A fully-qualified path representing Corpus resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromCorpusName(corpusName: string): string | number;
    /**
     * Return a fully-qualified corpusPermission resource name string.
     *
     * @param {string} corpus
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    corpusPermissionPath(corpus: string, permission: string): string;
    /**
     * Parse the corpus from CorpusPermission resource.
     *
     * @param {string} corpusPermissionName
     *   A fully-qualified path representing corpus_permission resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromCorpusPermissionName(corpusPermissionName: string): string | number;
    /**
     * Parse the permission from CorpusPermission resource.
     *
     * @param {string} corpusPermissionName
     *   A fully-qualified path representing corpus_permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromCorpusPermissionName(corpusPermissionName: string): string | number;
    /**
     * Return a fully-qualified document resource name string.
     *
     * @param {string} corpus
     * @param {string} document
     * @returns {string} Resource name string.
     */
    documentPath(corpus: string, document: string): string;
    /**
     * Parse the corpus from Document resource.
     *
     * @param {string} documentName
     *   A fully-qualified path representing Document resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromDocumentName(documentName: string): string | number;
    /**
     * Parse the document from Document resource.
     *
     * @param {string} documentName
     *   A fully-qualified path representing Document resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromDocumentName(documentName: string): string | number;
    /**
     * Return a fully-qualified file resource name string.
     *
     * @param {string} file
     * @returns {string} Resource name string.
     */
    filePath(file: string): string;
    /**
     * Parse the file from File resource.
     *
     * @param {string} fileName
     *   A fully-qualified path representing File resource.
     * @returns {string} A string representing the file.
     */
    matchFileFromFileName(fileName: string): string | number;
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(model: string): string;
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName: string): string | number;
    /**
     * Return a fully-qualified tunedModel resource name string.
     *
     * @param {string} tuned_model
     * @returns {string} Resource name string.
     */
    tunedModelPath(tunedModel: string): string;
    /**
     * Parse the tuned_model from TunedModel resource.
     *
     * @param {string} tunedModelName
     *   A fully-qualified path representing TunedModel resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelName(tunedModelName: string): string | number;
    /**
     * Return a fully-qualified tunedModelPermission resource name string.
     *
     * @param {string} tuned_model
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    tunedModelPermissionPath(tunedModel: string, permission: string): string;
    /**
     * Parse the tuned_model from TunedModelPermission resource.
     *
     * @param {string} tunedModelPermissionName
     *   A fully-qualified path representing tuned_model_permission resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelPermissionName(tunedModelPermissionName: string): string | number;
    /**
     * Parse the permission from TunedModelPermission resource.
     *
     * @param {string} tunedModelPermissionName
     *   A fully-qualified path representing tuned_model_permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromTunedModelPermissionName(tunedModelPermissionName: string): string | number;
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close(): Promise<void>;
}
