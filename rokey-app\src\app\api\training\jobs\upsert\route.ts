import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import type { NewTrainingJob } from '@/types/training';

// POST /api/training/jobs/upsert
// Safely creates or updates a training job to prevent duplicates and data loss
export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();

  try {
    const jobData = await request.json() as NewTrainingJob;
    const { custom_api_config_id, name, description, training_data, parameters } = jobData;

    if (!custom_api_config_id || !name) {
      return NextResponse.json({ 
        error: 'Missing required fields: custom_api_config_id, name' 
      }, { status: 400 });
    }

    console.log(`[Training Job Upsert] Processing upsert for config: ${custom_api_config_id}`);

    // Check if a training job already exists for this config
    const { data: existingJob, error: checkError } = await supabase
      .from('training_jobs')
      .select('id, name, description, status, training_data, parameters, created_at, updated_at')
      .eq('custom_api_config_id', custom_api_config_id)
      .eq('status', 'completed')
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (checkError) {
      console.error('[Training Job Upsert] Error checking existing job:', checkError);
      return NextResponse.json({
        error: 'Failed to check existing training job',
        details: checkError.message
      }, { status: 500 });
    }

    let result;
    let isNewJob = false;

    if (existingJob) {
      // Update existing job
      console.log(`[Training Job Upsert] Updating existing job: ${existingJob.id}`);

      const { data: updatedJob, error: updateError } = await supabase
        .from('training_jobs')
        .update({
          name,
          description,
          training_data,
          parameters,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingJob.id)
        .select()
        .single();

      if (updateError) {
        console.error('[Training Job Upsert] Error updating job:', updateError);
        return NextResponse.json({
          error: 'Failed to update training job',
          details: updateError.message
        }, { status: 500 });
      }

      result = updatedJob;
      isNewJob = false;
    } else {
      // Create new job
      console.log(`[Training Job Upsert] Creating new job for config: ${custom_api_config_id}`);

      const { data: newJob, error: createError } = await supabase
        .from('training_jobs')
        .insert({
          custom_api_config_id,
          name,
          description,
          training_data,
          parameters,
          status: 'completed',
          progress_percentage: 100,
          started_at: new Date().toISOString(),
          completed_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        console.error('[Training Job Upsert] Error creating job:', createError);
        return NextResponse.json({
          error: 'Failed to create training job',
          details: createError.message
        }, { status: 500 });
      }

      result = newJob;
      isNewJob = true;
    }

    console.log(`[Training Job Upsert] ${isNewJob ? 'Created new' : 'Updated existing'} training job:`, result.id);

    // Invalidate training cache for immediate effect
    if (!isNewJob) {
      try {
        const { trainingDataCache } = await import('@/lib/cache/trainingCache');
        const invalidated = trainingDataCache.invalidate(custom_api_config_id);
        console.log(`[Training Job Upsert] Cache invalidated for config: ${custom_api_config_id} (${invalidated ? 'success' : 'not cached'})`);
      } catch (error) {
        console.warn(`[Training Job Upsert] Cache invalidation error:`, error);
      }
    }

    // Return appropriate status code and response
    return NextResponse.json({
      id: result.id,
      custom_api_config_id: result.custom_api_config_id,
      name: result.name,
      description: result.description,
      status: result.status,
      training_data: result.training_data,
      parameters: result.parameters,
      created_at: result.created_at,
      updated_at: result.updated_at,
      operation: isNewJob ? 'created' : 'updated',
      message: isNewJob
        ? 'New training job created successfully'
        : 'Existing training job updated successfully - all files preserved'
    }, {
      status: isNewJob ? 201 : 200
    });

  } catch (e: Error) {
    console.error('[Training Job Upsert] Error:', e);
    if (e.name === 'SyntaxError') {
      return NextResponse.json({ 
        error: 'Invalid request body: Malformed JSON.' 
      }, { status: 400 });
    }
    return NextResponse.json({ 
      error: 'An unexpected error occurred', 
      details: e.message 
    }, { status: 500 });
  }
}
