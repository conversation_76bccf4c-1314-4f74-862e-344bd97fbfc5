{"interfaces": {"google.ai.generativelanguage.v1alpha.PermissionService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreatePermission": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetPermission": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListPermissions": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdatePermission": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeletePermission": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "TransferOwnership": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}