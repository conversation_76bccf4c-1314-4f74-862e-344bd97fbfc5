{"interfaces": {"google.ai.generativelanguage.v1alpha.RetrieverService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateCorpus": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetCorpus": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateCorpus": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteCorpus": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListCorpora": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "QueryCorpus": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateDocument": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetDocument": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateDocument": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteDocument": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListDocuments": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "QueryDocument": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateChunk": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchCreateChunks": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetChunk": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateChunk": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchUpdateChunks": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteChunk": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchDeleteChunks": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListChunks": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}