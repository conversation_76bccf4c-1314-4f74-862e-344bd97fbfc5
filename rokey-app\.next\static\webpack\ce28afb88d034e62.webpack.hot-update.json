{"c": ["app/layout", "webpack"], "r": [], "m": [null, "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/MapIcon.js", "(app-pages-browser)/./node_modules/next/dist/api/image.js", "(app-pages-browser)/./node_modules/next/dist/api/navigation.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Ccomponents%5C%5CConditionalLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Ccomponents%5C%5CDocumentTitleUpdater.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Ccomponents%5C%5CGlobalPrefetcher.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceTracker.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Cstyles%5C%5Cdesign-system.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "(app-pages-browser)/./node_modules/next/dist/client/image-component.js", "(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js", "(app-pages-browser)/./node_modules/next/dist/client/script.js", "(app-pages-browser)/./node_modules/next/dist/client/set-attributes-from-props.js", "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js", "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js", "(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-mode.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-blur-svg.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/side-effect.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js", "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}", "(app-pages-browser)/./src/app/globals.css", "(app-pages-browser)/./src/components/ConditionalLayout.tsx", "(app-pages-browser)/./src/components/DocumentTitleUpdater.tsx", "(app-pages-browser)/./src/components/GlobalPrefetcher.tsx", "(app-pages-browser)/./src/components/LayoutContent.tsx", "(app-pages-browser)/./src/components/Navbar.tsx", "(app-pages-browser)/./src/components/OptimisticPageLoader.tsx", "(app-pages-browser)/./src/components/PerformanceTracker.tsx", "(app-pages-browser)/./src/components/Sidebar.tsx", "(app-pages-browser)/./src/contexts/NavigationContext.tsx", "(app-pages-browser)/./src/contexts/SidebarContext.tsx", "(app-pages-browser)/./src/hooks/useAdvancedPreloading.ts", "(app-pages-browser)/./src/hooks/useBreadcrumb.ts", "(app-pages-browser)/./src/hooks/useChatHistory.ts", "(app-pages-browser)/./src/hooks/usePerformanceOptimization.ts", "(app-pages-browser)/./src/hooks/usePredictiveNavigation.ts", "(app-pages-browser)/./src/hooks/useRoutePrefetch.ts", "(app-pages-browser)/./src/styles/design-system.css", "(app-pages-browser)/./src/utils/advancedCache.ts", "(app-pages-browser)/./src/utils/cacheStrategy.ts"]}