import type * as gax from 'google-gax';
import type { Callback, CallOptions, Descriptors, ClientOptions, PaginationCallback } from 'google-gax';
import { Transform } from 'stream';
import * as protos from '../../protos/protos';
/**
 *  An API for semantic search over a corpus of user uploaded content.
 * @class
 * @memberof v1beta
 */
export declare class RetrieverServiceClient {
    private _terminated;
    private _opts;
    private _providedCustomServicePath;
    private _gaxModule;
    private _gaxGrpc;
    private _protos;
    private _defaults;
    private _universeDomain;
    private _servicePath;
    private _log;
    auth: gax.GoogleAuth;
    descriptors: Descriptors;
    warn: (code: string, message: string, warnType?: string) => void;
    innerApiCalls: {
        [name: string]: Function;
    };
    pathTemplates: {
        [name: string]: gax.PathTemplate;
    };
    retrieverServiceStub?: Promise<{
        [name: string]: Function;
    }>;
    /**
     * Construct an instance of RetrieverServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new RetrieverServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts?: ClientOptions, gaxInstance?: typeof gax | typeof gax.fallback);
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize(): Promise<{
        [name: string]: Function;
    }>;
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath(): string;
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint(): string;
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint(): string;
    get universeDomain(): string;
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port(): number;
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes(): never[];
    getProjectId(): Promise<string>;
    getProjectId(callback: Callback<string, undefined, undefined>): void;
    /**
     * Creates an empty `Corpus`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.ai.generativelanguage.v1beta.Corpus} request.corpus
     *   Required. The `Corpus` to create.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.Corpus|Corpus}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.create_corpus.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_CreateCorpus_async
     */
    createCorpus(request?: protos.google.ai.generativelanguage.v1beta.ICreateCorpusRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.ICorpus,
        (protos.google.ai.generativelanguage.v1beta.ICreateCorpusRequest | undefined),
        {} | undefined
    ]>;
    createCorpus(request: protos.google.ai.generativelanguage.v1beta.ICreateCorpusRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICorpus, protos.google.ai.generativelanguage.v1beta.ICreateCorpusRequest | null | undefined, {} | null | undefined>): void;
    createCorpus(request: protos.google.ai.generativelanguage.v1beta.ICreateCorpusRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICorpus, protos.google.ai.generativelanguage.v1beta.ICreateCorpusRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets information about a specific `Corpus`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the `Corpus`.
     *   Example: `corpora/my-corpus-123`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.Corpus|Corpus}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.get_corpus.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_GetCorpus_async
     */
    getCorpus(request?: protos.google.ai.generativelanguage.v1beta.IGetCorpusRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.ICorpus,
        protos.google.ai.generativelanguage.v1beta.IGetCorpusRequest | undefined,
        {} | undefined
    ]>;
    getCorpus(request: protos.google.ai.generativelanguage.v1beta.IGetCorpusRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICorpus, protos.google.ai.generativelanguage.v1beta.IGetCorpusRequest | null | undefined, {} | null | undefined>): void;
    getCorpus(request: protos.google.ai.generativelanguage.v1beta.IGetCorpusRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICorpus, protos.google.ai.generativelanguage.v1beta.IGetCorpusRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates a `Corpus`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.ai.generativelanguage.v1beta.Corpus} request.corpus
     *   Required. The `Corpus` to update.
     * @param {google.protobuf.FieldMask} request.updateMask
     *   Required. The list of fields to update.
     *   Currently, this only supports updating `display_name`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.Corpus|Corpus}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.update_corpus.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_UpdateCorpus_async
     */
    updateCorpus(request?: protos.google.ai.generativelanguage.v1beta.IUpdateCorpusRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.ICorpus,
        (protos.google.ai.generativelanguage.v1beta.IUpdateCorpusRequest | undefined),
        {} | undefined
    ]>;
    updateCorpus(request: protos.google.ai.generativelanguage.v1beta.IUpdateCorpusRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICorpus, protos.google.ai.generativelanguage.v1beta.IUpdateCorpusRequest | null | undefined, {} | null | undefined>): void;
    updateCorpus(request: protos.google.ai.generativelanguage.v1beta.IUpdateCorpusRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.ICorpus, protos.google.ai.generativelanguage.v1beta.IUpdateCorpusRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Deletes a `Corpus`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the `Corpus`.
     *   Example: `corpora/my-corpus-123`
     * @param {boolean} [request.force]
     *   Optional. If set to true, any `Document`s and objects related to this
     *   `Corpus` will also be deleted.
     *
     *   If false (the default), a `FAILED_PRECONDITION` error will be returned if
     *   `Corpus` contains any `Document`s.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.delete_corpus.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_DeleteCorpus_async
     */
    deleteCorpus(request?: protos.google.ai.generativelanguage.v1beta.IDeleteCorpusRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        (protos.google.ai.generativelanguage.v1beta.IDeleteCorpusRequest | undefined),
        {} | undefined
    ]>;
    deleteCorpus(request: protos.google.ai.generativelanguage.v1beta.IDeleteCorpusRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1beta.IDeleteCorpusRequest | null | undefined, {} | null | undefined>): void;
    deleteCorpus(request: protos.google.ai.generativelanguage.v1beta.IDeleteCorpusRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1beta.IDeleteCorpusRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Performs semantic search over a `Corpus`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the `Corpus` to query.
     *   Example: `corpora/my-corpus-123`
     * @param {string} request.query
     *   Required. Query string to perform semantic search.
     * @param {number[]} [request.metadataFilters]
     *   Optional. Filter for `Chunk` and `Document` metadata. Each `MetadataFilter`
     *   object should correspond to a unique key. Multiple `MetadataFilter` objects
     *   are joined by logical "AND"s.
     *
     *   Example query at document level:
     *   (year >= 2020 OR year < 2010) AND (genre = drama OR genre = action)
     *
     *   `MetadataFilter` object list:
     *    metadata_filters = [
     *    {key = "document.custom_metadata.year"
     *     conditions = [{int_value = 2020, operation = GREATER_EQUAL},
     *                   {int_value = 2010, operation = LESS}]},
     *    {key = "document.custom_metadata.year"
     *     conditions = [{int_value = 2020, operation = GREATER_EQUAL},
     *                   {int_value = 2010, operation = LESS}]},
     *    {key = "document.custom_metadata.genre"
     *     conditions = [{string_value = "drama", operation = EQUAL},
     *                   {string_value = "action", operation = EQUAL}]}]
     *
     *   Example query at chunk level for a numeric range of values:
     *   (year > 2015 AND year <= 2020)
     *
     *   `MetadataFilter` object list:
     *    metadata_filters = [
     *    {key = "chunk.custom_metadata.year"
     *     conditions = [{int_value = 2015, operation = GREATER}]},
     *    {key = "chunk.custom_metadata.year"
     *     conditions = [{int_value = 2020, operation = LESS_EQUAL}]}]
     *
     *   Note: "AND"s for the same key are only supported for numeric values. String
     *   values only support "OR"s for the same key.
     * @param {number} [request.resultsCount]
     *   Optional. The maximum number of `Chunk`s to return.
     *   The service may return fewer `Chunk`s.
     *
     *   If unspecified, at most 10 `Chunk`s will be returned.
     *   The maximum specified result count is 100.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.QueryCorpusResponse|QueryCorpusResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.query_corpus.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_QueryCorpus_async
     */
    queryCorpus(request?: protos.google.ai.generativelanguage.v1beta.IQueryCorpusRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IQueryCorpusResponse,
        (protos.google.ai.generativelanguage.v1beta.IQueryCorpusRequest | undefined),
        {} | undefined
    ]>;
    queryCorpus(request: protos.google.ai.generativelanguage.v1beta.IQueryCorpusRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.IQueryCorpusResponse, protos.google.ai.generativelanguage.v1beta.IQueryCorpusRequest | null | undefined, {} | null | undefined>): void;
    queryCorpus(request: protos.google.ai.generativelanguage.v1beta.IQueryCorpusRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.IQueryCorpusResponse, protos.google.ai.generativelanguage.v1beta.IQueryCorpusRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates an empty `Document`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Corpus` where this `Document` will be created.
     *   Example: `corpora/my-corpus-123`
     * @param {google.ai.generativelanguage.v1beta.Document} request.document
     *   Required. The `Document` to create.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.Document|Document}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.create_document.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_CreateDocument_async
     */
    createDocument(request?: protos.google.ai.generativelanguage.v1beta.ICreateDocumentRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IDocument,
        (protos.google.ai.generativelanguage.v1beta.ICreateDocumentRequest | undefined),
        {} | undefined
    ]>;
    createDocument(request: protos.google.ai.generativelanguage.v1beta.ICreateDocumentRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.IDocument, protos.google.ai.generativelanguage.v1beta.ICreateDocumentRequest | null | undefined, {} | null | undefined>): void;
    createDocument(request: protos.google.ai.generativelanguage.v1beta.ICreateDocumentRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.IDocument, protos.google.ai.generativelanguage.v1beta.ICreateDocumentRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets information about a specific `Document`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the `Document` to retrieve.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.Document|Document}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.get_document.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_GetDocument_async
     */
    getDocument(request?: protos.google.ai.generativelanguage.v1beta.IGetDocumentRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IDocument,
        (protos.google.ai.generativelanguage.v1beta.IGetDocumentRequest | undefined),
        {} | undefined
    ]>;
    getDocument(request: protos.google.ai.generativelanguage.v1beta.IGetDocumentRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.IDocument, protos.google.ai.generativelanguage.v1beta.IGetDocumentRequest | null | undefined, {} | null | undefined>): void;
    getDocument(request: protos.google.ai.generativelanguage.v1beta.IGetDocumentRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.IDocument, protos.google.ai.generativelanguage.v1beta.IGetDocumentRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates a `Document`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.ai.generativelanguage.v1beta.Document} request.document
     *   Required. The `Document` to update.
     * @param {google.protobuf.FieldMask} request.updateMask
     *   Required. The list of fields to update.
     *   Currently, this only supports updating `display_name` and
     *   `custom_metadata`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.Document|Document}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.update_document.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_UpdateDocument_async
     */
    updateDocument(request?: protos.google.ai.generativelanguage.v1beta.IUpdateDocumentRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IDocument,
        (protos.google.ai.generativelanguage.v1beta.IUpdateDocumentRequest | undefined),
        {} | undefined
    ]>;
    updateDocument(request: protos.google.ai.generativelanguage.v1beta.IUpdateDocumentRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.IDocument, protos.google.ai.generativelanguage.v1beta.IUpdateDocumentRequest | null | undefined, {} | null | undefined>): void;
    updateDocument(request: protos.google.ai.generativelanguage.v1beta.IUpdateDocumentRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.IDocument, protos.google.ai.generativelanguage.v1beta.IUpdateDocumentRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Deletes a `Document`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the `Document` to delete.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {boolean} [request.force]
     *   Optional. If set to true, any `Chunk`s and objects related to this
     *   `Document` will also be deleted.
     *
     *   If false (the default), a `FAILED_PRECONDITION` error will be returned if
     *   `Document` contains any `Chunk`s.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.delete_document.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_DeleteDocument_async
     */
    deleteDocument(request?: protos.google.ai.generativelanguage.v1beta.IDeleteDocumentRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        (protos.google.ai.generativelanguage.v1beta.IDeleteDocumentRequest | undefined),
        {} | undefined
    ]>;
    deleteDocument(request: protos.google.ai.generativelanguage.v1beta.IDeleteDocumentRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1beta.IDeleteDocumentRequest | null | undefined, {} | null | undefined>): void;
    deleteDocument(request: protos.google.ai.generativelanguage.v1beta.IDeleteDocumentRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1beta.IDeleteDocumentRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Performs semantic search over a `Document`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the `Document` to query.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {string} request.query
     *   Required. Query string to perform semantic search.
     * @param {number} [request.resultsCount]
     *   Optional. The maximum number of `Chunk`s to return.
     *   The service may return fewer `Chunk`s.
     *
     *   If unspecified, at most 10 `Chunk`s will be returned.
     *   The maximum specified result count is 100.
     * @param {number[]} [request.metadataFilters]
     *   Optional. Filter for `Chunk` metadata. Each `MetadataFilter` object should
     *   correspond to a unique key. Multiple `MetadataFilter` objects are joined by
     *   logical "AND"s.
     *
     *   Note: `Document`-level filtering is not supported for this request because
     *   a `Document` name is already specified.
     *
     *   Example query:
     *   (year >= 2020 OR year < 2010) AND (genre = drama OR genre = action)
     *
     *   `MetadataFilter` object list:
     *    metadata_filters = [
     *    {key = "chunk.custom_metadata.year"
     *     conditions = [{int_value = 2020, operation = GREATER_EQUAL},
     *                   {int_value = 2010, operation = LESS}},
     *    {key = "chunk.custom_metadata.genre"
     *     conditions = [{string_value = "drama", operation = EQUAL},
     *                   {string_value = "action", operation = EQUAL}}]
     *
     *   Example query for a numeric range of values:
     *   (year > 2015 AND year <= 2020)
     *
     *   `MetadataFilter` object list:
     *    metadata_filters = [
     *    {key = "chunk.custom_metadata.year"
     *     conditions = [{int_value = 2015, operation = GREATER}]},
     *    {key = "chunk.custom_metadata.year"
     *     conditions = [{int_value = 2020, operation = LESS_EQUAL}]}]
     *
     *   Note: "AND"s for the same key are only supported for numeric values. String
     *   values only support "OR"s for the same key.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.QueryDocumentResponse|QueryDocumentResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.query_document.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_QueryDocument_async
     */
    queryDocument(request?: protos.google.ai.generativelanguage.v1beta.IQueryDocumentRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IQueryDocumentResponse,
        (protos.google.ai.generativelanguage.v1beta.IQueryDocumentRequest | undefined),
        {} | undefined
    ]>;
    queryDocument(request: protos.google.ai.generativelanguage.v1beta.IQueryDocumentRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.IQueryDocumentResponse, protos.google.ai.generativelanguage.v1beta.IQueryDocumentRequest | null | undefined, {} | null | undefined>): void;
    queryDocument(request: protos.google.ai.generativelanguage.v1beta.IQueryDocumentRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.IQueryDocumentResponse, protos.google.ai.generativelanguage.v1beta.IQueryDocumentRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates a `Chunk`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Document` where this `Chunk` will be created.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {google.ai.generativelanguage.v1beta.Chunk} request.chunk
     *   Required. The `Chunk` to create.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.Chunk|Chunk}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.create_chunk.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_CreateChunk_async
     */
    createChunk(request?: protos.google.ai.generativelanguage.v1beta.ICreateChunkRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IChunk,
        (protos.google.ai.generativelanguage.v1beta.ICreateChunkRequest | undefined),
        {} | undefined
    ]>;
    createChunk(request: protos.google.ai.generativelanguage.v1beta.ICreateChunkRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.IChunk, protos.google.ai.generativelanguage.v1beta.ICreateChunkRequest | null | undefined, {} | null | undefined>): void;
    createChunk(request: protos.google.ai.generativelanguage.v1beta.ICreateChunkRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.IChunk, protos.google.ai.generativelanguage.v1beta.ICreateChunkRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Batch create `Chunk`s.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} [request.parent]
     *   Optional. The name of the `Document` where this batch of `Chunk`s will be
     *   created. The parent field in every `CreateChunkRequest` must match this
     *   value. Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {number[]} request.requests
     *   Required. The request messages specifying the `Chunk`s to create.
     *   A maximum of 100 `Chunk`s can be created in a batch.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.BatchCreateChunksResponse|BatchCreateChunksResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.batch_create_chunks.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_BatchCreateChunks_async
     */
    batchCreateChunks(request?: protos.google.ai.generativelanguage.v1beta.IBatchCreateChunksRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IBatchCreateChunksResponse,
        (protos.google.ai.generativelanguage.v1beta.IBatchCreateChunksRequest | undefined),
        {} | undefined
    ]>;
    batchCreateChunks(request: protos.google.ai.generativelanguage.v1beta.IBatchCreateChunksRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.IBatchCreateChunksResponse, protos.google.ai.generativelanguage.v1beta.IBatchCreateChunksRequest | null | undefined, {} | null | undefined>): void;
    batchCreateChunks(request: protos.google.ai.generativelanguage.v1beta.IBatchCreateChunksRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.IBatchCreateChunksResponse, protos.google.ai.generativelanguage.v1beta.IBatchCreateChunksRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets information about a specific `Chunk`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the `Chunk` to retrieve.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc/chunks/some-chunk`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.Chunk|Chunk}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.get_chunk.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_GetChunk_async
     */
    getChunk(request?: protos.google.ai.generativelanguage.v1beta.IGetChunkRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IChunk,
        protos.google.ai.generativelanguage.v1beta.IGetChunkRequest | undefined,
        {} | undefined
    ]>;
    getChunk(request: protos.google.ai.generativelanguage.v1beta.IGetChunkRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.IChunk, protos.google.ai.generativelanguage.v1beta.IGetChunkRequest | null | undefined, {} | null | undefined>): void;
    getChunk(request: protos.google.ai.generativelanguage.v1beta.IGetChunkRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.IChunk, protos.google.ai.generativelanguage.v1beta.IGetChunkRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates a `Chunk`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.ai.generativelanguage.v1beta.Chunk} request.chunk
     *   Required. The `Chunk` to update.
     * @param {google.protobuf.FieldMask} request.updateMask
     *   Required. The list of fields to update.
     *   Currently, this only supports updating `custom_metadata` and `data`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.Chunk|Chunk}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.update_chunk.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_UpdateChunk_async
     */
    updateChunk(request?: protos.google.ai.generativelanguage.v1beta.IUpdateChunkRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IChunk,
        (protos.google.ai.generativelanguage.v1beta.IUpdateChunkRequest | undefined),
        {} | undefined
    ]>;
    updateChunk(request: protos.google.ai.generativelanguage.v1beta.IUpdateChunkRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.IChunk, protos.google.ai.generativelanguage.v1beta.IUpdateChunkRequest | null | undefined, {} | null | undefined>): void;
    updateChunk(request: protos.google.ai.generativelanguage.v1beta.IUpdateChunkRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.IChunk, protos.google.ai.generativelanguage.v1beta.IUpdateChunkRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Batch update `Chunk`s.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} [request.parent]
     *   Optional. The name of the `Document` containing the `Chunk`s to update.
     *   The parent field in every `UpdateChunkRequest` must match this value.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {number[]} request.requests
     *   Required. The request messages specifying the `Chunk`s to update.
     *   A maximum of 100 `Chunk`s can be updated in a batch.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.ai.generativelanguage.v1beta.BatchUpdateChunksResponse|BatchUpdateChunksResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.batch_update_chunks.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_BatchUpdateChunks_async
     */
    batchUpdateChunks(request?: protos.google.ai.generativelanguage.v1beta.IBatchUpdateChunksRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IBatchUpdateChunksResponse,
        (protos.google.ai.generativelanguage.v1beta.IBatchUpdateChunksRequest | undefined),
        {} | undefined
    ]>;
    batchUpdateChunks(request: protos.google.ai.generativelanguage.v1beta.IBatchUpdateChunksRequest, options: CallOptions, callback: Callback<protos.google.ai.generativelanguage.v1beta.IBatchUpdateChunksResponse, protos.google.ai.generativelanguage.v1beta.IBatchUpdateChunksRequest | null | undefined, {} | null | undefined>): void;
    batchUpdateChunks(request: protos.google.ai.generativelanguage.v1beta.IBatchUpdateChunksRequest, callback: Callback<protos.google.ai.generativelanguage.v1beta.IBatchUpdateChunksResponse, protos.google.ai.generativelanguage.v1beta.IBatchUpdateChunksRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Deletes a `Chunk`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the `Chunk` to delete.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc/chunks/some-chunk`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.delete_chunk.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_DeleteChunk_async
     */
    deleteChunk(request?: protos.google.ai.generativelanguage.v1beta.IDeleteChunkRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        (protos.google.ai.generativelanguage.v1beta.IDeleteChunkRequest | undefined),
        {} | undefined
    ]>;
    deleteChunk(request: protos.google.ai.generativelanguage.v1beta.IDeleteChunkRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1beta.IDeleteChunkRequest | null | undefined, {} | null | undefined>): void;
    deleteChunk(request: protos.google.ai.generativelanguage.v1beta.IDeleteChunkRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1beta.IDeleteChunkRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Batch delete `Chunk`s.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} [request.parent]
     *   Optional. The name of the `Document` containing the `Chunk`s to delete.
     *   The parent field in every `DeleteChunkRequest` must match this value.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {number[]} request.requests
     *   Required. The request messages specifying the `Chunk`s to delete.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.batch_delete_chunks.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_BatchDeleteChunks_async
     */
    batchDeleteChunks(request?: protos.google.ai.generativelanguage.v1beta.IBatchDeleteChunksRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        (protos.google.ai.generativelanguage.v1beta.IBatchDeleteChunksRequest | undefined),
        {} | undefined
    ]>;
    batchDeleteChunks(request: protos.google.ai.generativelanguage.v1beta.IBatchDeleteChunksRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1beta.IBatchDeleteChunksRequest | null | undefined, {} | null | undefined>): void;
    batchDeleteChunks(request: protos.google.ai.generativelanguage.v1beta.IBatchDeleteChunksRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.ai.generativelanguage.v1beta.IBatchDeleteChunksRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Lists all `Corpora` owned by the user.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Corpora` to return (per page).
     *   The service may return fewer `Corpora`.
     *
     *   If unspecified, at most 10 `Corpora` will be returned.
     *   The maximum size limit is 20 `Corpora` per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListCorpora` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListCorpora`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.ai.generativelanguage.v1beta.Corpus|Corpus}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listCorporaAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listCorpora(request?: protos.google.ai.generativelanguage.v1beta.IListCorporaRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.ICorpus[],
        protos.google.ai.generativelanguage.v1beta.IListCorporaRequest | null,
        protos.google.ai.generativelanguage.v1beta.IListCorporaResponse
    ]>;
    listCorpora(request: protos.google.ai.generativelanguage.v1beta.IListCorporaRequest, options: CallOptions, callback: PaginationCallback<protos.google.ai.generativelanguage.v1beta.IListCorporaRequest, protos.google.ai.generativelanguage.v1beta.IListCorporaResponse | null | undefined, protos.google.ai.generativelanguage.v1beta.ICorpus>): void;
    listCorpora(request: protos.google.ai.generativelanguage.v1beta.IListCorporaRequest, callback: PaginationCallback<protos.google.ai.generativelanguage.v1beta.IListCorporaRequest, protos.google.ai.generativelanguage.v1beta.IListCorporaResponse | null | undefined, protos.google.ai.generativelanguage.v1beta.ICorpus>): void;
    /**
     * Equivalent to `listCorpora`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Corpora` to return (per page).
     *   The service may return fewer `Corpora`.
     *
     *   If unspecified, at most 10 `Corpora` will be returned.
     *   The maximum size limit is 20 `Corpora` per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListCorpora` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListCorpora`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1beta.Corpus|Corpus} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listCorporaAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listCorporaStream(request?: protos.google.ai.generativelanguage.v1beta.IListCorporaRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listCorpora`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Corpora` to return (per page).
     *   The service may return fewer `Corpora`.
     *
     *   If unspecified, at most 10 `Corpora` will be returned.
     *   The maximum size limit is 20 `Corpora` per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListCorpora` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListCorpora`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1beta.Corpus|Corpus}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.list_corpora.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_ListCorpora_async
     */
    listCorporaAsync(request?: protos.google.ai.generativelanguage.v1beta.IListCorporaRequest, options?: CallOptions): AsyncIterable<protos.google.ai.generativelanguage.v1beta.ICorpus>;
    /**
     * Lists all `Document`s in a `Corpus`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Corpus` containing `Document`s.
     *   Example: `corpora/my-corpus-123`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Document`s to return (per page).
     *   The service may return fewer `Document`s.
     *
     *   If unspecified, at most 10 `Document`s will be returned.
     *   The maximum size limit is 20 `Document`s per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListDocuments` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListDocuments`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.ai.generativelanguage.v1beta.Document|Document}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listDocumentsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listDocuments(request?: protos.google.ai.generativelanguage.v1beta.IListDocumentsRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IDocument[],
        protos.google.ai.generativelanguage.v1beta.IListDocumentsRequest | null,
        protos.google.ai.generativelanguage.v1beta.IListDocumentsResponse
    ]>;
    listDocuments(request: protos.google.ai.generativelanguage.v1beta.IListDocumentsRequest, options: CallOptions, callback: PaginationCallback<protos.google.ai.generativelanguage.v1beta.IListDocumentsRequest, protos.google.ai.generativelanguage.v1beta.IListDocumentsResponse | null | undefined, protos.google.ai.generativelanguage.v1beta.IDocument>): void;
    listDocuments(request: protos.google.ai.generativelanguage.v1beta.IListDocumentsRequest, callback: PaginationCallback<protos.google.ai.generativelanguage.v1beta.IListDocumentsRequest, protos.google.ai.generativelanguage.v1beta.IListDocumentsResponse | null | undefined, protos.google.ai.generativelanguage.v1beta.IDocument>): void;
    /**
     * Equivalent to `listDocuments`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Corpus` containing `Document`s.
     *   Example: `corpora/my-corpus-123`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Document`s to return (per page).
     *   The service may return fewer `Document`s.
     *
     *   If unspecified, at most 10 `Document`s will be returned.
     *   The maximum size limit is 20 `Document`s per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListDocuments` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListDocuments`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1beta.Document|Document} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listDocumentsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listDocumentsStream(request?: protos.google.ai.generativelanguage.v1beta.IListDocumentsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listDocuments`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Corpus` containing `Document`s.
     *   Example: `corpora/my-corpus-123`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Document`s to return (per page).
     *   The service may return fewer `Document`s.
     *
     *   If unspecified, at most 10 `Document`s will be returned.
     *   The maximum size limit is 20 `Document`s per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListDocuments` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListDocuments`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1beta.Document|Document}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.list_documents.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_ListDocuments_async
     */
    listDocumentsAsync(request?: protos.google.ai.generativelanguage.v1beta.IListDocumentsRequest, options?: CallOptions): AsyncIterable<protos.google.ai.generativelanguage.v1beta.IDocument>;
    /**
     * Lists all `Chunk`s in a `Document`.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Document` containing `Chunk`s.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Chunk`s to return (per page).
     *   The service may return fewer `Chunk`s.
     *
     *   If unspecified, at most 10 `Chunk`s will be returned.
     *   The maximum size limit is 100 `Chunk`s per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListChunks` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListChunks`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.ai.generativelanguage.v1beta.Chunk|Chunk}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listChunksAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listChunks(request?: protos.google.ai.generativelanguage.v1beta.IListChunksRequest, options?: CallOptions): Promise<[
        protos.google.ai.generativelanguage.v1beta.IChunk[],
        protos.google.ai.generativelanguage.v1beta.IListChunksRequest | null,
        protos.google.ai.generativelanguage.v1beta.IListChunksResponse
    ]>;
    listChunks(request: protos.google.ai.generativelanguage.v1beta.IListChunksRequest, options: CallOptions, callback: PaginationCallback<protos.google.ai.generativelanguage.v1beta.IListChunksRequest, protos.google.ai.generativelanguage.v1beta.IListChunksResponse | null | undefined, protos.google.ai.generativelanguage.v1beta.IChunk>): void;
    listChunks(request: protos.google.ai.generativelanguage.v1beta.IListChunksRequest, callback: PaginationCallback<protos.google.ai.generativelanguage.v1beta.IListChunksRequest, protos.google.ai.generativelanguage.v1beta.IListChunksResponse | null | undefined, protos.google.ai.generativelanguage.v1beta.IChunk>): void;
    /**
     * Equivalent to `listChunks`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Document` containing `Chunk`s.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Chunk`s to return (per page).
     *   The service may return fewer `Chunk`s.
     *
     *   If unspecified, at most 10 `Chunk`s will be returned.
     *   The maximum size limit is 100 `Chunk`s per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListChunks` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListChunks`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1beta.Chunk|Chunk} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listChunksAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listChunksStream(request?: protos.google.ai.generativelanguage.v1beta.IListChunksRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listChunks`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Document` containing `Chunk`s.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Chunk`s to return (per page).
     *   The service may return fewer `Chunk`s.
     *
     *   If unspecified, at most 10 `Chunk`s will be returned.
     *   The maximum size limit is 100 `Chunk`s per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListChunks` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListChunks`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1beta.Chunk|Chunk}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.list_chunks.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_ListChunks_async
     */
    listChunksAsync(request?: protos.google.ai.generativelanguage.v1beta.IListChunksRequest, options?: CallOptions): AsyncIterable<protos.google.ai.generativelanguage.v1beta.IChunk>;
    /**
     * Return a fully-qualified cachedContent resource name string.
     *
     * @param {string} id
     * @returns {string} Resource name string.
     */
    cachedContentPath(id: string): string;
    /**
     * Parse the id from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the id.
     */
    matchIdFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Return a fully-qualified chunk resource name string.
     *
     * @param {string} corpus
     * @param {string} document
     * @param {string} chunk
     * @returns {string} Resource name string.
     */
    chunkPath(corpus: string, document: string, chunk: string): string;
    /**
     * Parse the corpus from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromChunkName(chunkName: string): string | number;
    /**
     * Parse the document from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromChunkName(chunkName: string): string | number;
    /**
     * Parse the chunk from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the chunk.
     */
    matchChunkFromChunkName(chunkName: string): string | number;
    /**
     * Return a fully-qualified corpus resource name string.
     *
     * @param {string} corpus
     * @returns {string} Resource name string.
     */
    corpusPath(corpus: string): string;
    /**
     * Parse the corpus from Corpus resource.
     *
     * @param {string} corpusName
     *   A fully-qualified path representing Corpus resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromCorpusName(corpusName: string): string | number;
    /**
     * Return a fully-qualified corpusPermission resource name string.
     *
     * @param {string} corpus
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    corpusPermissionPath(corpus: string, permission: string): string;
    /**
     * Parse the corpus from CorpusPermission resource.
     *
     * @param {string} corpusPermissionName
     *   A fully-qualified path representing corpus_permission resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromCorpusPermissionName(corpusPermissionName: string): string | number;
    /**
     * Parse the permission from CorpusPermission resource.
     *
     * @param {string} corpusPermissionName
     *   A fully-qualified path representing corpus_permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromCorpusPermissionName(corpusPermissionName: string): string | number;
    /**
     * Return a fully-qualified document resource name string.
     *
     * @param {string} corpus
     * @param {string} document
     * @returns {string} Resource name string.
     */
    documentPath(corpus: string, document: string): string;
    /**
     * Parse the corpus from Document resource.
     *
     * @param {string} documentName
     *   A fully-qualified path representing Document resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromDocumentName(documentName: string): string | number;
    /**
     * Parse the document from Document resource.
     *
     * @param {string} documentName
     *   A fully-qualified path representing Document resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromDocumentName(documentName: string): string | number;
    /**
     * Return a fully-qualified file resource name string.
     *
     * @param {string} file
     * @returns {string} Resource name string.
     */
    filePath(file: string): string;
    /**
     * Parse the file from File resource.
     *
     * @param {string} fileName
     *   A fully-qualified path representing File resource.
     * @returns {string} A string representing the file.
     */
    matchFileFromFileName(fileName: string): string | number;
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(model: string): string;
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName: string): string | number;
    /**
     * Return a fully-qualified tunedModel resource name string.
     *
     * @param {string} tuned_model
     * @returns {string} Resource name string.
     */
    tunedModelPath(tunedModel: string): string;
    /**
     * Parse the tuned_model from TunedModel resource.
     *
     * @param {string} tunedModelName
     *   A fully-qualified path representing TunedModel resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelName(tunedModelName: string): string | number;
    /**
     * Return a fully-qualified tunedModelPermission resource name string.
     *
     * @param {string} tuned_model
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    tunedModelPermissionPath(tunedModel: string, permission: string): string;
    /**
     * Parse the tuned_model from TunedModelPermission resource.
     *
     * @param {string} tunedModelPermissionName
     *   A fully-qualified path representing tuned_model_permission resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelPermissionName(tunedModelPermissionName: string): string | number;
    /**
     * Parse the permission from TunedModelPermission resource.
     *
     * @param {string} tunedModelPermissionName
     *   A fully-qualified path representing tuned_model_permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromTunedModelPermissionName(tunedModelPermissionName: string): string | number;
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close(): Promise<void>;
}
