import * as v1 from './v1';
import * as v1alpha from './v1alpha';
import * as v1beta from './v1beta';
import * as v1beta2 from './v1beta2';
import * as v1beta3 from './v1beta3';
declare const CacheServiceClient: typeof v1beta.CacheServiceClient;
type CacheServiceClient = v1beta.CacheServiceClient;
declare const DiscussServiceClient: typeof v1beta.DiscussServiceClient;
type DiscussServiceClient = v1beta.DiscussServiceClient;
declare const FileServiceClient: typeof v1beta.FileServiceClient;
type FileServiceClient = v1beta.FileServiceClient;
declare const GenerativeServiceClient: typeof v1beta.GenerativeServiceClient;
type GenerativeServiceClient = v1beta.GenerativeServiceClient;
declare const ModelServiceClient: typeof v1beta.ModelServiceClient;
type ModelServiceClient = v1beta.ModelServiceClient;
declare const PermissionServiceClient: typeof v1beta.PermissionServiceClient;
type PermissionServiceClient = v1beta.PermissionServiceClient;
declare const PredictionServiceClient: typeof v1beta.PredictionServiceClient;
type PredictionServiceClient = v1beta.PredictionServiceClient;
declare const RetrieverServiceClient: typeof v1beta.RetrieverServiceClient;
type RetrieverServiceClient = v1beta.RetrieverServiceClient;
declare const TextServiceClient: typeof v1beta.TextServiceClient;
type TextServiceClient = v1beta.TextServiceClient;
export { v1, v1alpha, v1beta, v1beta2, v1beta3, CacheServiceClient, DiscussServiceClient, FileServiceClient, GenerativeServiceClient, ModelServiceClient, PermissionServiceClient, PredictionServiceClient, RetrieverServiceClient, TextServiceClient, };
declare const _default: {
    v1: typeof v1;
    v1alpha: typeof v1alpha;
    v1beta: typeof v1beta;
    v1beta2: typeof v1beta2;
    v1beta3: typeof v1beta3;
    CacheServiceClient: typeof v1beta.CacheServiceClient;
    DiscussServiceClient: typeof v1beta.DiscussServiceClient;
    FileServiceClient: typeof v1beta.FileServiceClient;
    GenerativeServiceClient: typeof v1beta.GenerativeServiceClient;
    ModelServiceClient: typeof v1beta.ModelServiceClient;
    PermissionServiceClient: typeof v1beta.PermissionServiceClient;
    PredictionServiceClient: typeof v1beta.PredictionServiceClient;
    RetrieverServiceClient: typeof v1beta.RetrieverServiceClient;
    TextServiceClient: typeof v1beta.TextServiceClient;
};
export default _default;
import * as protos from '../protos/protos';
export { protos };
