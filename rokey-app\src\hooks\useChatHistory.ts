'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { type ChatConversation } from '@/types/chatHistory';

interface ChatHistoryCache {
  data: ChatConversation[];
  timestamp: number;
  isStale: boolean;
}

interface UseChatHistoryOptions {
  configId: string | null;
  enablePrefetch?: boolean;
  cacheTimeout?: number;
  staleTimeout?: number;
}

interface UseChatHistoryReturn {
  chatHistory: ChatConversation[];
  isLoading: boolean;
  isStale: boolean;
  error: string | null;
  refetch: (force?: boolean) => Promise<void>;
  prefetch: (configId: string) => Promise<void>;
  invalidateCache: (configId?: string) => void;
  getCacheStats: () => { size: number; hits: number; misses: number };
}

// Global cache shared across all instances
const globalCache = new Map<string, ChatHistoryCache>();
const cacheStats = { hits: 0, misses: 0 };

// Background prefetch queue
const prefetchQueue = new Set<string>();
let isPrefetching = false;

export const useChatHistory = ({
  configId,
  enablePrefetch = true,
  cacheTimeout = 300000, // 5 minutes
  staleTimeout = 30000   // 30 seconds
}: UseChatHistoryOptions): UseChatHistoryReturn => {
  const [chatHistory, setChatHistory] = useState<ChatConversation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isStale, setIsStale] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const abortControllerRef = useRef<AbortController | null>(null);
  const lastFetchRef = useRef<string | null>(null);

  // Fetch function with aggressive caching
  const fetchChatHistory = useCallback(async (
    targetConfigId: string,
    force = false,
    background = false
  ): Promise<ChatConversation[]> => {
    const cacheKey = targetConfigId;
    const cached = globalCache.get(cacheKey);
    const now = Date.now();

    // Return cached data if valid and not forced
    if (!force && cached && (now - cached.timestamp) < cacheTimeout) {
      cacheStats.hits++;
      
      // Check if data is stale but still valid
      const isDataStale = (now - cached.timestamp) > staleTimeout;
      if (isDataStale && !cached.isStale) {
        // Mark as stale and trigger background refresh
        cached.isStale = true;
        if (enablePrefetch) {
          prefetchQueue.add(targetConfigId);
          processPrefetchQueue();
        }
      }
      
      return cached.data;
    }

    cacheStats.misses++;

    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      const url = `/api/chat/conversations?custom_api_config_id=${targetConfigId}`;
      const response = await fetch(url, {
        signal: abortControllerRef.current.signal,
        headers: {
          'Cache-Control': 'no-cache',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch chat history: ${response.status} ${response.statusText}`);
      }

      const conversations: ChatConversation[] = await response.json();

      // Update cache
      globalCache.set(cacheKey, {
        data: conversations,
        timestamp: now,
        isStale: false
      });

      console.log(`📦 Chat history ${background ? 'prefetched' : 'loaded'} for config ${targetConfigId}: ${conversations.length} conversations`);
      
      return conversations;

    } catch (err: Error) {
      if (err.name === 'AbortError') {
        console.log('Chat history fetch aborted');
        throw err;
      }
      
      console.error('Error fetching chat history:', err);
      
      // Return stale data if available
      if (cached && cached.data.length > 0) {
        console.log('Returning stale chat history data due to fetch error');
        return cached.data;
      }
      
      throw err;
    }
  }, [cacheTimeout, staleTimeout, enablePrefetch]);

  // Background prefetch processor
  const processPrefetchQueue = useCallback(async () => {
    if (isPrefetching || prefetchQueue.size === 0) return;
    
    isPrefetching = true;
    
    try {
      const configsToFetch = Array.from(prefetchQueue);
      prefetchQueue.clear();
      
      for (const configId of configsToFetch) {
        try {
          await fetchChatHistory(configId, true, true);
          // Small delay between prefetches
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.warn(`Failed to prefetch chat history for config ${configId}:`, error);
        }
      }
    } finally {
      isPrefetching = false;
    }
  }, [fetchChatHistory]);

  // Main refetch function
  const refetch = useCallback(async (force = false) => {
    if (!configId) return;
    
    // Optimistic update: show cached data immediately if available
    const cached = globalCache.get(configId);
    if (!force && cached && cached.data.length > 0) {
      setChatHistory(cached.data);
      setIsStale(cached.isStale);
      setError(null);
    }

    setIsLoading(true);
    lastFetchRef.current = configId;

    try {
      const conversations = await fetchChatHistory(configId, force);
      
      // Only update if this is still the current config
      if (lastFetchRef.current === configId) {
        setChatHistory(conversations);
        setIsStale(false);
        setError(null);
      }
    } catch (err: Error) {
      if (err.name !== 'AbortError' && lastFetchRef.current === configId) {
        setError(`Failed to load chat history: ${err.message}`);
      }
    } finally {
      if (lastFetchRef.current === configId) {
        setIsLoading(false);
      }
    }
  }, [configId, fetchChatHistory]);

  // Prefetch function for external use
  const prefetch = useCallback(async (targetConfigId: string) => {
    if (!enablePrefetch) return;
    
    prefetchQueue.add(targetConfigId);
    processPrefetchQueue();
  }, [enablePrefetch, processPrefetchQueue]);

  // Cache invalidation
  const invalidateCache = useCallback((targetConfigId?: string) => {
    if (targetConfigId) {
      globalCache.delete(targetConfigId);
      console.log(`🗑️ Invalidated chat history cache for config ${targetConfigId}`);
    } else {
      globalCache.clear();
      console.log('🗑️ Cleared all chat history cache');
    }
  }, []);

  // Cache stats
  const getCacheStats = useCallback(() => ({
    size: globalCache.size,
    hits: cacheStats.hits,
    misses: cacheStats.misses
  }), []);

  // Effect to load data when configId changes
  useEffect(() => {
    if (configId) {
      refetch();
    } else {
      setChatHistory([]);
      setIsLoading(false);
      setError(null);
      setIsStale(false);
    }
  }, [configId, refetch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    chatHistory,
    isLoading,
    isStale,
    error,
    refetch,
    prefetch,
    invalidateCache,
    getCacheStats
  };
};

// Hook for prefetching chat history on navigation
export const useChatHistoryPrefetch = () => {
  const prefetchedConfigs = useRef(new Set<string>());

  const prefetchChatHistory = useCallback(async (configId: string) => {
    if (prefetchedConfigs.current.has(configId)) return;
    
    prefetchedConfigs.current.add(configId);
    prefetchQueue.add(configId);
    
    // Process queue after a short delay to batch requests
    setTimeout(() => {
      if (prefetchQueue.size > 0) {
        const processPrefetch = async () => {
          if (isPrefetching) return;
          isPrefetching = true;
          
          try {
            const configsToFetch = Array.from(prefetchQueue);
            prefetchQueue.clear();
            
            for (const id of configsToFetch) {
              try {
                const url = `/api/chat/conversations?custom_api_config_id=${id}`;
                const response = await fetch(url, {
                  headers: { 'X-Prefetch': 'true' }
                });
                
                if (response.ok) {
                  const conversations = await response.json();
                  globalCache.set(id, {
                    data: conversations,
                    timestamp: Date.now(),
                    isStale: false
                  });
                  console.log(`📦 Prefetched chat history for config ${id}`);
                }
              } catch (error) {
                console.warn(`Failed to prefetch chat history for ${id}:`, error);
              }
              
              // Small delay between requests
              await new Promise(resolve => setTimeout(resolve, 100));
            }
          } finally {
            isPrefetching = false;
          }
        };
        
        processPrefetch();
      }
    }, 200);
  }, []);

  return { prefetchChatHistory };
};

// Export cache utilities for debugging
export const chatHistoryCache = {
  getCache: () => globalCache,
  getStats: () => cacheStats,
  clear: () => {
    globalCache.clear();
    cacheStats.hits = 0;
    cacheStats.misses = 0;
  }
};
