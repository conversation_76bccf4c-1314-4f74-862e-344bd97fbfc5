// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ai.generativelanguage.v1beta2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/ai/generativelanguage/apiv1beta2/generativelanguagepb;generativelanguagepb";
option java_multiple_files = true;
option java_outer_classname = "CitationProto";
option java_package = "com.google.ai.generativelanguage.v1beta2";

// A collection of source attributions for a piece of content.
message CitationMetadata {
  // Citations to sources for a specific response.
  repeated CitationSource citation_sources = 1;
}

// A citation to a source for a portion of a specific response.
message CitationSource {
  // Optional. Start of segment of the response that is attributed to this
  // source.
  //
  // Index indicates the start of the segment, measured in bytes.
  optional int32 start_index = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. End of the attributed segment, exclusive.
  optional int32 end_index = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. URI that is attributed as a source for a portion of the text.
  optional string uri = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. License for the GitHub project that is attributed as a source for
  // segment.
  //
  // License info is required for code citations.
  optional string license = 4 [(google.api.field_behavior) = OPTIONAL];
}
