"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[274],{4654:(e,s,a)=>{a.d(s,{f:()=>l});var r=a(95155);a(12115);var t=a(11485);let l=e=>{let{orchestrationComplete:s,onMaximize:a,isCanvasOpen:l,isCanvasMinimized:n}=e;return(0,r.jsxs)("div",{className:"flex justify-start group mb-16 mt-8 ".concat(l&&!n?"-ml-96":""," ").concat(l&&!n?"ml-8":""),children:[(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,r.jsx)("div",{className:"".concat(l&&!n?"max-w-[80%]":"max-w-[65%]"," relative"),children:(0,r.jsx)("div",{onClick:a,className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-2xl rounded-bl-lg shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-[1.02] min-w-[320px] ring-2 ring-blue-300/60 hover:ring-blue-300/80 shadow-blue-500/20",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(t.v,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-sm",children:"AI Team Collaboration"}),(0,r.jsx)("p",{className:"text-xs opacity-90",children:s?"Completed - Click to view results":"Multi-Role Orchestration in progress"})]}),(0,r.jsxs)("div",{className:"flex-shrink-0",children:[!s&&(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),s&&(0,r.jsx)(t.B,{className:"w-5 h-5"})]})]})})})]})}},8413:(e,s,a)=>{a.d(s,{A:()=>i});var r=a(95155),t=a(12115),l=a(21208);let n=new Map;function i(e){let{configId:s,onRetry:a,className:i="",disabled:o=!1}=e,[d,c]=(0,t.useState)(!1),[m,x]=(0,t.useState)([]),[u,g]=(0,t.useState)(!1),[h,p]=(0,t.useState)(!1),b=(0,t.useRef)(null),f=(0,t.useCallback)(async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(s){if(e){let e=n.get(s);if(e&&Date.now()-e.timestamp<3e5){x(e.keys),p(!0);return}}g(!0);try{let e=await fetch("/api/keys?custom_config_id=".concat(s));if(e.ok){let a=(await e.json()).filter(e=>"active"===e.status);n.set(s,{keys:a,timestamp:Date.now()}),x(a),p(!0)}}catch(e){}finally{g(!1)}}},[s]);(0,t.useEffect)(()=>{s&&!h&&f(!0)},[s,f,h]),(0,t.useEffect)(()=>{let e=e=>{b.current&&!b.current.contains(e.target)&&c(!1)};return d&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[d]);let j=e=>{c(!1),a(e)};return(0,r.jsxs)("div",{className:"relative ".concat(i),ref:b,children:[(0,r.jsxs)("button",{onClick:()=>{d||0!==m.length||h||f(!0),c(!d)},disabled:o,className:"\n          flex items-center space-x-1 p-1.5 rounded transition-all duration-200 cursor-pointer\n          ".concat(o?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:text-gray-700 hover:bg-white/20","\n        "),title:"Retry with different model",children:[(0,r.jsx)(l.E,{className:"w-4 h-4 stroke-2 ".concat(u?"animate-spin":"")}),(0,r.jsx)(l.D,{className:"w-3 h-3 stroke-2"})]}),d&&(0,r.jsx)("div",{className:"absolute bottom-full left-0 mb-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-scale-in origin-bottom-left",children:(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-100",children:[(0,r.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"Retry Options"}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),f(!1)},disabled:u,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50",title:"Refresh available models",children:(0,r.jsx)(l.E,{className:"w-3 h-3 ".concat(u?"animate-spin":"")})})]}),(0,r.jsxs)("button",{onClick:()=>j(),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,r.jsx)(l.E,{className:"w-4 h-4 text-gray-500"}),(0,r.jsx)("span",{children:"Retry with same model"})]}),(m.length>0||u)&&(0,r.jsx)("div",{className:"border-t border-gray-100 my-1"}),u&&(0,r.jsxs)("div",{className:"px-3 py-2 text-sm text-gray-500 flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),(0,r.jsx)("span",{children:"Loading models..."})]}),m.map(e=>(0,r.jsxs)("button",{onClick:()=>j(e.id),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex flex-col disabled:opacity-50",disabled:u,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsx)("span",{className:"font-medium",children:e.label}),(0,r.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.provider})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-0.5",children:["Temperature: ",e.temperature]})]},e.id)),!u&&0===m.length&&h&&(0,r.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No alternative models available"}),m.length>0&&!u&&(0,r.jsxs)("div",{className:"px-3 py-1 text-xs text-gray-400 border-t border-gray-100 flex items-center justify-between",children:[(0,r.jsxs)("span",{children:[m.length," model",1!==m.length?"s":""," available"]}),(()=>{let e=n.get(s);return e&&Date.now()-e.timestamp<3e5?(0,r.jsx)("span",{className:"text-green-500 text-xs",children:"●"}):null})()]})]})})]})}},14446:(e,s,a)=>{a.d(s,{Ay:()=>t,CE:()=>l});var r=a(95155);function t(){return(0,r.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"})}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]})}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-xl p-4 animate-pulse",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full animate-pulse"})]})]})},e))})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"card p-8 min-h-[600px]",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("div",{className:"h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full animate-pulse"})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 animate-pulse"})]})]},e))})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,r.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-4",children:[1,2,3,4,5].map(e=>(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse"})]},e))}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-40 animate-pulse"})})]})]})})]})]})})}function l(){return(0,r.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})},e))}),(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]})]})]})})}a(12115)},34094:(e,s,a)=>{a.d(s,{default:()=>z});var r=a(95155),t=a(35695),l=a(22261),n=a(99323),i=a(29315),o=a(34962);function d(){let{toggleSidebar:e}=(0,l.c)(),{breadcrumb:s}=(0,o.rT)();return(0,r.jsx)("nav",{className:"header border-b border-gray-200 bg-white/95 backdrop-blur-sm",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("button",{onClick:e,className:"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200",title:"Toggle sidebar",children:(0,r.jsx)(i.tK,{className:"h-6 w-6 text-gray-600"})}),(0,r.jsx)("div",{className:"lg:hidden",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RoKey"})}),(0,r.jsxs)("div",{className:"hidden lg:flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)("span",{children:s.title}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:s.subtitle})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,r.jsx)("div",{className:"hidden xl:block",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search...",className:"w-64 pl-10 pr-4 py-2.5 text-sm bg-white border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200"}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)("svg",{className:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})}),(0,r.jsxs)("button",{className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 relative",children:[(0,r.jsx)(i.XF,{className:"h-5 w-5 text-gray-600"}),(0,r.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"})]}),(0,r.jsx)("button",{className:"hidden sm:block p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200",children:(0,r.jsx)(i.Vy,{className:"h-5 w-5 text-gray-600"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-semibold text-sm",children:"DU"})}),(0,r.jsxs)("div",{className:"hidden md:block",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Demo User"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Free Plan"})]})]})]})]})})})}var c=a(6874),m=a.n(c),x=a(66766),u=a(12115),g=a(42597),h=a(14097),p=a(37843),b=a(24403),f=a(42724);let j=[{href:"/dashboard",label:"Dashboard",icon:g.fA,iconSolid:h.fA,description:"Overview & analytics"},{href:"/my-models",label:"My Models",icon:g.RY,iconSolid:h.RY,description:"API key management"},{href:"/playground",label:"Playground",icon:g.cu,iconSolid:h.cu,description:"Test your models"},{href:"/routing-setup",label:"Routing Setup",icon:g.sR,iconSolid:h.sR,description:"Configure routing"},{href:"/logs",label:"Logs",icon:g.AQ,iconSolid:h.AQ,description:"Request history"},{href:"/training",label:"Prompt Engineering",icon:g.tl,iconSolid:h.tl,description:"Custom prompts"},{href:"/analytics",label:"Analytics",icon:g.r9,iconSolid:h.r9,description:"Advanced insights"}];function v(){let e=(0,t.usePathname)(),{isCollapsed:s,isHovered:a,isHoverDisabled:i,setHovered:o}=(0,l.c)(),{navigateOptimistically:d}=(0,n.bu)()||{navigateOptimistically:()=>{}},{prefetchOnHover:c}=(0,p.C)(),{prefetchWhenIdle:g}=(0,p.e)(),{prefetchChatHistory:h}=(0,b.l2)(),{predictions:v,isLearning:y}=(0,f.x)(),N=(0,f.G)();(0,u.useEffect)(()=>{let s=j.map(e=>e.href),a=v.slice(0,2),r=N.filter(e=>"high"===e.priority).map(e=>e.route).slice(0,2);return g([...a,...r,...s.filter(s=>s!==e&&!a.includes(s)&&!r.includes(s)),"/playground","/logs"].slice(0,6))},[e,g,v,N,y]);let w=!s||a;return(0,r.jsx)("aside",{className:"sidebar flex flex-col h-screen flex-shrink-0 transition-all duration-200 ease-out z-50 ".concat(w?"w-64":"w-16"),onMouseEnter:()=>!i&&o(!0),onMouseLeave:()=>!i&&o(!1),children:(0,r.jsx)("div",{className:"flex-1 relative overflow-hidden",children:(0,r.jsxs)("div",{className:"p-6 transition-all duration-200 ease-out ".concat(w?"px-6":"px-3"),children:[(0,r.jsx)("div",{className:"mb-8 pt-4 transition-all duration-200 ease-out ".concat(w?"":"text-center"),children:(0,r.jsxs)("div",{className:"relative overflow-hidden",children:[(0,r.jsx)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-0 scale-75 -translate-y-2":"opacity-100 scale-100 translate-y-0"," ").concat(w?"absolute":"relative"," w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-0.5"),children:(0,r.jsx)(x.default,{src:"/roukey_logo.png",alt:"RouKey",width:28,height:28,className:"object-cover"})}),(0,r.jsxs)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-75 translate-y-2"," ").concat(w?"relative":"absolute top-0 left-0 w-full"),children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white tracking-tight whitespace-nowrap",children:"RoKey"}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1 whitespace-nowrap",children:"Smart LLM Router"})]})]})}),(0,r.jsx)("nav",{className:"space-y-2",children:j.map(s=>{let a=e===s.href||e.startsWith(s.href+"/"),t=a?s.iconSolid:s.icon,l=v.includes(s.href),n=N.find(e=>e.route===s.href),i="/playground"===s.href?{onMouseEnter:()=>{if("/playground"===s.href){c(s.href,50).onMouseEnter();let e=new URLSearchParams(window.location.search).get("config");e&&h(e)}}}:c(s.href,50);return(0,r.jsx)(m(),{href:s.href,onClick:e=>{e.preventDefault(),d(s.href)},className:"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ".concat(a?"active":""," ").concat(w?"":"collapsed"),title:w?void 0:s.label,...i,children:(0,r.jsxs)("div",{className:"relative flex items-center w-full overflow-hidden",children:[(0,r.jsxs)("div",{className:"relative flex items-center justify-center transition-all duration-200 ease-out ".concat(w?"w-5 h-5 mr-3":"w-10 h-10 rounded-xl"," ").concat(!w&&a?"bg-white shadow-sm":w?"":"bg-transparent hover:bg-white/10"),children:[(0,r.jsx)(t,{className:"transition-all duration-200 ease-out ".concat("h-5 w-5"," ").concat(a?"text-orange-500":"text-white")}),l&&!a&&(0,r.jsx)("div",{className:"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ".concat(w?"-top-1 -right-1 w-2 h-2":"-top-1 -right-1 w-3 h-3"),title:"Predicted next destination"})]}),(0,r.jsxs)("div",{className:"flex-1 transition-all duration-200 ease-out ".concat(w?"opacity-100 translate-x-0 max-w-full":"opacity-0 translate-x-4 max-w-0"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between whitespace-nowrap",children:[(0,r.jsx)("div",{className:"font-medium text-sm",children:s.label}),n&&!a&&(0,r.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-full ml-2 ".concat("high"===n.priority?"bg-blue-500/20 text-blue-300":"bg-gray-500/20 text-gray-300"),children:"high"===n.priority?"!":"\xb7"})]}),(0,r.jsx)("div",{className:"text-xs transition-colors duration-200 whitespace-nowrap ".concat(a?"text-orange-400":"text-gray-400"),children:n?n.reason:s.description})]})]})},s.href)})})]})})})}let y=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"})]},e))}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})]})]}),N=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"bg-white p-8 rounded-2xl border-2",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3 mx-auto"})]}),(0,r.jsx)("div",{className:"space-y-3 mb-8",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]},e))})]}),w=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,r.jsx)("div",{className:"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsx)("div",{className:"py-20",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[1,2,3,4,5,6].map(e=>(0,r.jsx)("div",{className:"bg-white rounded-2xl p-8 border",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-xl"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mb-3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-4"}),(0,r.jsx)("div",{className:"space-y-2",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"},e))})]})]})},e))})})]}),k=()=>(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]})]})}),C=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded mt-4"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded mt-4"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]}),_=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg border",children:(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})})]});function S(e){let s,{targetRoute:a,children:l}=e,[i,o]=(0,u.useState)(!0),[d,c]=(0,u.useState)(!1),m=(0,t.usePathname)(),x=(0,u.useRef)(),{isPageCached:g}=(0,n.bu)()||{isPageCached:()=>!1};return((0,u.useEffect)(()=>(m===a&&(x.current=setTimeout(()=>{c(!0),setTimeout(()=>o(!1),100)},g(a)?50:200)),()=>{x.current&&clearTimeout(x.current)}),[m,a,g]),(0,u.useEffect)(()=>{o(!0),c(!1)},[a]),m!==a&&i||m===a&&i&&!d)?(0,r.jsx)("div",{className:"optimistic-loading-container",children:(s=a).startsWith("/dashboard")?(0,r.jsx)(y,{}):s.startsWith("/pricing")?(0,r.jsx)(N,{}):s.startsWith("/features")?(0,r.jsx)(w,{}):s.startsWith("/auth/")?(0,r.jsx)(k,{}):s.startsWith("/playground")?(0,r.jsx)(C,{}):(0,r.jsx)(_,{})}):(0,r.jsx)("div",{className:"transition-opacity duration-300 ".concat(d?"opacity-100":"opacity-0"),children:l})}var E=a(42126);function A(e){let{children:s}=e,{isCollapsed:a,collapseSidebar:t}=(0,l.c)(),{isNavigating:i,targetRoute:o,isPageCached:c}=(0,n.bu)()||{isNavigating:!1,targetRoute:null,isPageCached:()=>!1};return(0,E.v)({maxConcurrent:2,idleTimeout:1500,backgroundDelay:3e3}),(0,r.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,r.jsx)("div",{className:"hidden lg:block",children:(0,r.jsx)(v,{})}),(0,r.jsxs)("div",{className:"lg:hidden fixed inset-0 z-50 ".concat(a?"pointer-events-none":""),children:[(0,r.jsx)("div",{onClick:t,className:"absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer ".concat(a?"opacity-0":"opacity-50")}),(0,r.jsx)("div",{className:"absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out ".concat(a?"-translate-x-full":"translate-x-0"),children:(0,r.jsx)(v,{})})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden min-w-0",children:[(0,r.jsx)(d,{}),(0,r.jsx)("main",{className:"flex-1 overflow-y-auto content-area",children:(0,r.jsx)("div",{className:"p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto w-full",children:(0,r.jsx)("div",{className:"page-transition",children:i&&o?(0,r.jsx)(S,{targetRoute:o,children:s}):s})})})]})]})}function z(e){let{children:s}=e,a=(0,t.usePathname)();return"/"===a||a.startsWith("/pricing")||a.startsWith("/features")||a.startsWith("/about")||a.startsWith("/auth/")?(0,r.jsx)(r.Fragment,{children:s}):(0,r.jsx)(l.G,{children:(0,r.jsx)(n.i9,{children:(0,r.jsx)(A,{children:s})})})}},38050:(e,s,a)=>{a.d(s,{default:()=>i});var r=a(12115),t=a(35695),l=a(5777),n=a(44042);function i(e){let{enableUserBehaviorTracking:s=!0,enableNavigationTracking:a=!0,enableInteractionTracking:i=!0}=e,o=(0,t.usePathname)(),d=(0,r.useRef)(""),c=(0,r.useRef)(0),{exportMetrics:m}=(0,n.D)("PerformanceTracker");return(0,r.useEffect)(()=>{if(!a)return;let e=d.current;e&&e!==o&&(l.zf.trackNavigation(e,o),performance.now(),c.current),d.current=o,c.current=performance.now()},[o,a]),(0,r.useEffect)(()=>{if(!i)return;let e=e=>{let s=e.target;if("mouseenter"===e.type&&"A"===s.tagName){let e=s.getAttribute("href");e&&e.startsWith("/")&&l.zf.schedulePrefetch(e)}if("click"===e.type&&("BUTTON"===s.tagName||s.closest("button"))){var a;let e=(null==(a=s.textContent)?void 0:a.trim())||"Unknown";e.toLowerCase().includes("get started")||e.toLowerCase().includes("sign up")?l.zf.schedulePrefetch("/auth/signup"):e.toLowerCase().includes("pricing")?l.zf.schedulePrefetch("/pricing"):e.toLowerCase().includes("features")&&l.zf.schedulePrefetch("/features")}};return document.addEventListener("mouseenter",e,!0),document.addEventListener("click",e,!0),()=>{document.removeEventListener("mouseenter",e,!0),document.removeEventListener("click",e,!0)}},[i]),(0,r.useEffect)(()=>{let e;if(!s)return;let a=!1,r=0,t=()=>{a||(a=!0,performance.now());let s=window.scrollY/(document.body.scrollHeight-window.innerHeight)*100;r=Math.max(r,s),clearTimeout(e),e=setTimeout(()=>{a=!1,performance.now(),r>80&&("/"===o?(l.zf.schedulePrefetch("/pricing"),l.zf.schedulePrefetch("/features")):"/features"===o&&l.zf.schedulePrefetch("/auth/signup")),r=0},150)},n=performance.now(),i=()=>{performance.now()-n>1e4&&("/"===o?l.zf.schedulePrefetch("/auth/signup"):"/pricing"===o&&l.zf.schedulePrefetch("/auth/signup"))};window.addEventListener("scroll",t,{passive:!0});let d=()=>{document.hidden&&i()};document.addEventListener("visibilitychange",d);let c=()=>{i()};return window.addEventListener("beforeunload",c),()=>{clearTimeout(e),window.removeEventListener("scroll",t),document.removeEventListener("visibilitychange",d),window.removeEventListener("beforeunload",c),i()}},[o,s,m]),(0,r.useEffect)(()=>{if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let s=e.getEntries();s[s.length-1].startTime});e.observe({entryTypes:["largest-contentful-paint"]});let s=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.processingStart&&e.startTime&&(e.processingStart,e.startTime)})});s.observe({entryTypes:["first-input"]});let a=0,r=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.hadRecentInput||(a+=e.value)})});return r.observe({entryTypes:["layout-shift"]}),()=>{e.disconnect(),s.disconnect(),r.disconnect()}}},[]),null}},43456:(e,s,a)=>{a.d(s,{A:()=>d});var r=a(95155),t=a(11518),l=a.n(t),n=a(12115),i=a(10747);let o={initializing:{icon:i.P,text:"Initializing",description:"Starting up systems",bgColor:"bg-gradient-to-r from-slate-50 to-gray-50",iconColor:"text-slate-600",borderColor:"border-slate-200/60",glowColor:"shadow-slate-200/50",gradientFrom:"from-slate-400",gradientTo:"to-gray-400",duration:200},analyzing:{icon:i.$p,text:"Analyzing",description:"Understanding your request",bgColor:"bg-gradient-to-r from-cyan-50 to-blue-50",iconColor:"text-cyan-600",borderColor:"border-cyan-200/60",glowColor:"shadow-cyan-200/50",gradientFrom:"from-cyan-400",gradientTo:"to-blue-400",duration:300},routing:{icon:i.EF,text:"Smart routing",description:"Finding optimal path",bgColor:"bg-gradient-to-r from-indigo-50 to-purple-50",iconColor:"text-indigo-600",borderColor:"border-indigo-200/60",glowColor:"shadow-indigo-200/50",gradientFrom:"from-indigo-400",gradientTo:"to-purple-400",duration:400},complexity_analysis:{icon:i.XL,text:"Analyzing complexity",description:"Evaluating request depth",bgColor:"bg-gradient-to-r from-amber-50 to-yellow-50",iconColor:"text-amber-600",borderColor:"border-amber-200/60",glowColor:"shadow-amber-200/50",gradientFrom:"from-amber-400",gradientTo:"to-yellow-400",duration:500},role_classification:{icon:i.Gg,text:"Assembling specialists",description:"Building expert team",bgColor:"bg-gradient-to-r from-violet-50 to-purple-50",iconColor:"text-violet-600",borderColor:"border-violet-200/60",glowColor:"shadow-violet-200/50",gradientFrom:"from-violet-400",gradientTo:"to-purple-400",duration:600},preparing:{icon:i.DQ,text:"Preparing",description:"Setting up processing",bgColor:"bg-gradient-to-r from-orange-50 to-amber-50",iconColor:"text-orange-600",borderColor:"border-orange-200/60",glowColor:"shadow-orange-200/50",gradientFrom:"from-orange-400",gradientTo:"to-amber-400",duration:300},connecting:{icon:i.nr,text:"Connecting",description:"Establishing AI link",bgColor:"bg-gradient-to-r from-rose-50 to-pink-50",iconColor:"text-rose-600",borderColor:"border-rose-200/60",glowColor:"shadow-rose-200/50",gradientFrom:"from-rose-400",gradientTo:"to-pink-400",duration:400},generating:{icon:i.Y3,text:"Thinking deeply",description:"AI processing in progress",bgColor:"bg-gradient-to-r from-emerald-50 to-teal-50",iconColor:"text-emerald-600",borderColor:"border-emerald-200/60",glowColor:"shadow-emerald-200/50",gradientFrom:"from-emerald-400",gradientTo:"to-teal-400",duration:800},typing:{icon:i.R2,text:"Streaming response",description:"Delivering your answer",bgColor:"bg-gradient-to-r from-green-50 to-emerald-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-emerald-400"},finalizing:{icon:i.BZ,text:"Finalizing",description:"Adding finishing touches",bgColor:"bg-gradient-to-r from-teal-50 to-cyan-50",iconColor:"text-teal-600",borderColor:"border-teal-200/60",glowColor:"shadow-teal-200/50",gradientFrom:"from-teal-400",gradientTo:"to-cyan-400",duration:200},complete:{icon:i.Zu,text:"Complete",description:"Response delivered",bgColor:"bg-gradient-to-r from-green-50 to-lime-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-lime-400",duration:100}};function d(e){let{currentStage:s,isStreaming:a=!1,className:t="",onStageChange:i,orchestrationStatus:d}=e,[c,m]=(0,n.useState)(s),[x,u]=(0,n.useState)(!1),g=o[c],h=g.icon;return(0,n.useEffect)(()=>{s!==c&&(u(!0),setTimeout(()=>{m(s),u(!1),null==i||i(s)},200))},[s,c,i]),(0,r.jsxs)("div",{className:"jsx-f56d70faa8a01b64 "+"flex justify-start ".concat(t),children:[(0,r.jsxs)("div",{className:"jsx-f56d70faa8a01b64 relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0",children:[(0,r.jsx)("div",{style:{animation:x?"spin 0.6s linear infinite":"spin 1.2s linear infinite",borderImage:"conic-gradient(from 0deg, transparent 0%, ".concat(g.iconColor.replace("text-","")," 25%, transparent 50%, ").concat(g.iconColor.replace("text-","")," 75%, transparent 100%) 1"),filter:"drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))"},className:"jsx-f56d70faa8a01b64 absolute -inset-1.5 w-9 h-9 rounded-full border-[3px] border-transparent animate-spin"}),(0,r.jsx)("div",{style:{animation:x?"spin 0.8s linear infinite reverse":"spin 1.6s linear infinite reverse",borderImage:"conic-gradient(from 180deg, transparent 0%, ".concat(g.iconColor.replace("text-","")," 30%, transparent 60%, ").concat(g.iconColor.replace("text-","")," 90%, transparent 100%) 1"),opacity:.8},className:"jsx-f56d70faa8a01b64 absolute -inset-1 w-8 h-8 rounded-full border-[2px] border-transparent animate-spin"}),(0,r.jsx)("div",{style:{borderColor:g.iconColor.replace("text-",""),opacity:.6,animation:"pulse 2s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 absolute -inset-0.5 w-7 h-7 rounded-full border border-transparent animate-pulse"}),(0,r.jsx)("div",{style:{boxShadow:"0 0 12px ".concat(g.iconColor.replace("text-",""),"40, 0 0 24px ").concat(g.iconColor.replace("text-",""),"20")},className:"jsx-f56d70faa8a01b64 "+"relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 ".concat(g.bgColor," border-2 ").concat(g.borderColor," shadow-lg backdrop-blur-sm"),children:(0,r.jsx)(h,{className:"jsx-f56d70faa8a01b64 "+"w-3.5 h-3.5 transition-all duration-500 ".concat(g.iconColor," ").concat(x?"scale-125 rotate-12":"scale-100"," drop-shadow-lg")})})]}),(0,r.jsxs)("div",{className:"jsx-f56d70faa8a01b64 "+"max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 ".concat(g.bgColor," ").concat(g.borderColor," border ").concat(g.glowColor," shadow-sm backdrop-blur-sm"),children:[(0,r.jsx)("div",{className:"jsx-f56d70faa8a01b64 flex items-center space-x-1.5",children:(0,r.jsxs)("div",{className:"jsx-f56d70faa8a01b64 transition-all duration-500",children:[(0,r.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"text-xs font-semibold transition-colors duration-500 ".concat(g.iconColor," tracking-wide"),children:d||g.text}),a&&"typing"===c&&!d&&(0,r.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"ml-1.5 text-[10px] opacity-80 ".concat(g.iconColor," font-medium"),children:"• Live"}),d&&(0,r.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"ml-1.5 text-[10px] opacity-80 ".concat(g.iconColor," font-medium"),children:"• Orchestrating"})]})}),("generating"===c||"typing"===c)&&(0,r.jsx)("div",{className:"jsx-f56d70faa8a01b64 mt-2",children:(0,r.jsx)("div",{className:"jsx-f56d70faa8a01b64 h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20",children:(0,r.jsx)("div",{style:{width:"typing"===c?"100%":"60%",animation:"typing"===c?"progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite":"progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 "+"h-full rounded-full transition-all duration-1000 bg-gradient-to-r ".concat(g.gradientFrom," ").concat(g.gradientTo," relative overflow-hidden"),children:(0,r.jsx)("div",{style:{animation:"progressShine 2s linear infinite",transform:"skewX(-20deg)"},className:"jsx-f56d70faa8a01b64 absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"})})})})]}),(0,r.jsx)(l(),{id:"f56d70faa8a01b64",children:"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}"})]})}},50956:(e,s,a)=>{a.d(s,{A:()=>o});var r=a(95155),t=a(12115),l=a(6874),n=a.n(l),i=a(35695);function o(e){let{href:s,children:a,className:l="",prefetch:o=!0}=e,d=(0,i.useRouter)();return(0,r.jsx)(n(),{href:s,className:l,onClick:e=>{e.preventDefault(),(0,t.startTransition)(()=>{d.push(s)})},prefetch:o,children:a})}},52469:(e,s,a)=>{a.d(s,{default:()=>n});var r=a(12115),t=a(35695);let l=["/features","/pricing","/about","/auth/signin","/auth/signup"];function n(){let e=(0,t.useRouter)();return(0,r.useEffect)(()=>{let s=()=>{l.forEach(s=>{e.prefetch(s)})};"requestIdleCallback"in window?window.requestIdleCallback(s,{timeout:2e3}):setTimeout(s,100)},[e]),null}},62074:(e,s,a)=>{a.d(s,{w:()=>x});var r=a(95155),t=a(12115),l=a(70036),n=a(60875);let i=e=>{var s,a;let{message:l}=e,i=e=>{switch(e){case"assignment":return(0,r.jsx)(n.fl,{className:"w-3 h-3 text-blue-500"});case"completion":return(0,r.jsx)(n.C1,{className:"w-3 h-3 text-green-500"});case"handoff":return(0,r.jsx)(n.fl,{className:"w-3 h-3 text-purple-500"});default:return null}},o="moderator"===l.sender,d=(e=>{if(!e)return"from-blue-500 to-blue-600";let s=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return s[e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]})(l.roleId);return(0,r.jsx)("div",{className:"flex ".concat("justify-start"," mb-4"),children:(0,r.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ".concat(d," flex items-center justify-center text-white shadow-sm"),children:(s=l.sender,l.roleId,"moderator"===s?(0,r.jsx)(n.BZ,{className:"w-4 h-4"}):(0,r.jsx)(n.YE,{className:"w-4 h-4"}))}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("span",{className:"text-sm font-semibold ".concat(o?"text-blue-700":"text-gray-700"),children:l.senderName}),i(l.type)&&(0,r.jsx)("div",{className:"flex items-center",children:i(l.type)}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:l.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),(0,r.jsx)("div",{className:"inline-block px-4 py-3 rounded-2xl shadow-sm ".concat(o?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"," ").concat("completion"===l.type?"border-green-200 bg-green-50":"assignment"===l.type?"border-blue-200 bg-blue-50":"handoff"===l.type?"border-purple-200 bg-purple-50":""),children:(0,r.jsx)("div",{className:"text-sm leading-relaxed ".concat(o?"text-blue-900":"text-gray-800"," ").concat("completion"===l.type?"text-green-900":"assignment"===l.type?"text-blue-900":"handoff"===l.type?"text-purple-900":""),children:l.content.split("\n").map((e,s)=>(0,r.jsxs)(t.Fragment,{children:[e,s<l.content.split("\n").length-1&&(0,r.jsx)("br",{})]},s))})}),"message"!==l.type&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat("assignment"===l.type?"bg-blue-100 text-blue-800":"completion"===l.type?"bg-green-100 text-green-800":"handoff"===l.type?"bg-purple-100 text-purple-800":"clarification"===l.type?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:["assignment"===l.type&&"\uD83D\uDCCB Task Assignment","completion"===l.type&&"✅ Task Complete","handoff"===l.type&&"\uD83D\uDD04 Handoff","clarification"===l.type&&"❓ Clarification"]})})]})]})})};var o=a(82880);let d=e=>{let{senderName:s,roleId:a}=e,t=(e=>{if(!e||"moderator"===e)return"from-blue-500 to-blue-600";let s=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return s[e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]})(a),l=!a||"moderator"===a;return(0,r.jsx)("div",{className:"flex justify-start mb-4 opacity-75",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ".concat(t," flex items-center justify-center text-white shadow-sm animate-pulse"),children:(e=>e&&"moderator"!==e?(0,r.jsx)(o.Y,{className:"w-4 h-4"}):(0,r.jsx)(o.B,{className:"w-4 h-4"}))(a)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("span",{className:"text-sm font-semibold ".concat(l?"text-blue-700":"text-gray-700"),children:s}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let s=["is thinking...","is working on this...","is analyzing...","is processing...","is crafting a response..."];return s[e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]})(s)})]}),(0,r.jsx)("div",{className:"inline-block px-4 py-3 rounded-2xl shadow-sm ".concat(l?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"),children:(0,r.jsx)("div",{className:"flex items-center space-x-1",children:(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]})})})]})]})})},c=e=>{let{executionId:s,events:a,isConnected:l,error:n,isComplete:o}=e,[c,m]=(0,t.useState)([]),[x,u]=(0,t.useState)(new Set),g=(0,t.useRef)(null);return((0,t.useEffect)(()=>{var e;null==(e=g.current)||e.scrollIntoView({behavior:"smooth"})},[c]),(0,t.useEffect)(()=>{let e=[],r=new Set;a.forEach((a,t)=>{var l,n,i,o,d,c,m,x,u,g,h;let p=new Date(a.timestamp||Date.now()),b="".concat(s,"-").concat(t);switch(a.type){case"orchestration_started":e.push({id:b,sender:"moderator",senderName:"Moderator",content:"\uD83C\uDFAC Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.",timestamp:p,type:"message"});break;case"task_decomposed":let f=((null==(l=a.data)?void 0:l.steps)||[]).map(e=>"\uD83E\uDD16 @".concat(e.roleId," - ").concat(e.modelName||"AI Specialist")).join("\n");e.push({id:b,sender:"moderator",senderName:"Moderator",content:"\uD83D\uDCCB I've analyzed the task and assembled this expert team:\n\n".concat(f,"\n\nLet's begin the collaboration!"),timestamp:p,type:"assignment"});break;case"step_assigned":e.push({id:b,sender:"moderator",senderName:"Moderator",roleId:a.role_id,content:"\uD83C\uDFAF @".concat(a.role_id,", you're up! ").concat((null==(n=a.data)?void 0:n.commentary)||"Please begin your specialized work on this task."),timestamp:p,type:"assignment"});break;case"moderator_assignment":e.push({id:b,sender:"moderator",senderName:"Moderator",roleId:a.role_id,content:(null==(i=a.data)?void 0:i.message)||"\uD83C\uDFAF @".concat(a.role_id,", you're up! Please begin your specialized work on this task."),timestamp:p,type:"assignment"});break;case"specialist_acknowledgment":e.push({id:b,sender:"specialist",senderName:a.role_id||"Specialist",roleId:a.role_id,content:(null==(o=a.data)?void 0:o.message)||"✅ Understood! I'm ".concat(a.role_id," and I'll handle this task with expertise. Starting work now..."),timestamp:p,type:"message"});break;case"step_started":case"step_progress":a.role_id&&r.add(a.role_id);break;case"specialist_message":e.push({id:b,sender:"specialist",senderName:a.role_id||"Specialist",roleId:a.role_id,content:"".concat((null==(d=a.data)?void 0:d.message)||"\uD83C\uDF89 Perfect! I've completed my part of the task. Here's what I've delivered:","\n\n").concat((null==(c=a.data)?void 0:c.output)||"Task completed successfully!"),timestamp:p,type:"completion"});break;case"step_completed":a.role_id&&r.delete(a.role_id);break;case"handoff_message":e.push({id:b,sender:"moderator",senderName:"Moderator",content:(null==(m=a.data)?void 0:m.message)||"✨ Excellent work, @".concat(null==(x=a.data)?void 0:x.fromRole,"! Quality looks great. Now passing to @").concat(null==(u=a.data)?void 0:u.toRole,"..."),timestamp:p,type:"handoff"});break;case"synthesis_started":e.push({id:b,sender:"moderator",senderName:"Moderator",content:(null==(g=a.data)?void 0:g.message)||"\uD83E\uDDE9 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...",timestamp:p,type:"message"}),r.add("moderator");break;case"synthesis_complete":r.delete("moderator"),e.push({id:b,sender:"moderator",senderName:"Moderator",content:(null==(h=a.data)?void 0:h.message)||"\uD83C\uDF8A Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!",timestamp:p,type:"completion"})}}),m(e),u(r)},[a,s]),n)?(0,r.jsx)("div",{className:"flex-1 flex items-center justify-center p-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Connection Error"}),(0,r.jsx)("p",{className:"text-gray-600",children:n})]})}):(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsx)("div",{className:"px-4 py-2 text-xs font-medium ".concat(l?"bg-green-50 text-green-700 border-b border-green-100":"bg-yellow-50 text-yellow-700 border-b border-yellow-100"),children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(l?"bg-green-500":"bg-yellow-500")}),(0,r.jsx)("span",{children:l?"Connected to AI Team":"Connecting..."})]})}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[0===c.length&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-blue-600 animate-pulse",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsx)("p",{className:"text-gray-500",children:"Waiting for AI team to start collaboration..."})]}),c.map(e=>(0,r.jsx)(i,{message:e},e.id)),Array.from(x).map(e=>(0,r.jsx)(d,{senderName:e,roleId:"moderator"!==e?e:void 0},e)),(0,r.jsx)("div",{ref:g})]})]})};var m=a(306);let x=e=>{let{executionId:s,onComplete:a,onError:n,onCanvasStateChange:i,forceMaximize:o=!1}=e,[d,x]=(0,t.useState)(!0),[u,g]=(0,t.useState)(!1),[h,p]=(0,t.useState)(!1),[b,f]=(0,t.useState)(""),{events:j,isConnected:v,error:y}=(0,l.LJ)(s);(0,t.useEffect)(()=>{let e=j.find(e=>"synthesis_complete"===e.type);if(e&&!h){var s;p(!0);let r=(null==(s=e.data)?void 0:s.result)||"Orchestration completed successfully";f(r),a&&a(r)}},[j,h,a]),(0,t.useEffect)(()=>{y&&n&&n(y)},[y,n]);let N=()=>{g(!1),x(!0),null==i||i(!0,!1)};return((0,t.useEffect)(()=>{null==i||i(d,u)},[d,u,i]),(0,t.useEffect)(()=>{o&&u&&N()},[o,u]),u||!d)?null:(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"fixed top-0 right-0 h-full w-1/2 bg-gradient-to-br from-gray-900 via-black to-gray-900 shadow-2xl z-[9999] transform transition-all duration-500 ease-out border-l border-orange-500/20 ".concat(d&&!u?"translate-x-0":"translate-x-full"),children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500/10 via-transparent to-orange-500/10 pointer-events-none"}),(0,r.jsx)("div",{className:"absolute left-0 top-0 h-full w-[2px] bg-gradient-to-b from-transparent via-orange-500 to-transparent animate-pulse"}),(0,r.jsxs)("div",{className:"relative flex items-center justify-between p-6 border-b border-orange-500/20 bg-gradient-to-r from-black/80 via-gray-900/90 to-black/80 backdrop-blur-sm",children:[(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-orange-500/50 to-transparent"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,r.jsx)(m.vQ,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-orange-500/30 rounded-xl blur-md -z-10 animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"font-bold text-white text-lg tracking-wide",children:"AI Team Collaboration"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(h?"bg-green-400":"bg-orange-400"," animate-pulse")}),(0,r.jsx)("p",{className:"text-sm text-gray-300 font-medium",children:h?"Mission Complete":"Team Active"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1.5 bg-orange-500/10 border border-orange-500/20 rounded-full",children:[(0,r.jsx)(m.BZ,{className:"w-4 h-4 text-orange-400"}),(0,r.jsx)("span",{className:"text-xs text-orange-300 font-medium",children:"LIVE"})]}),(0,r.jsxs)("button",{onClick:()=>{g(!0),null==i||i(!1,!0)},className:"group relative p-2.5 text-gray-400 hover:text-white hover:bg-orange-500/20 rounded-xl transition-all duration-300 border border-transparent hover:border-orange-500/30","aria-label":"Minimize canvas",children:[(0,r.jsx)(m.QG,{className:"w-5 h-5 transition-transform group-hover:scale-110"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-orange-500/20 rounded-xl opacity-0 group-hover:opacity-100 blur-sm transition-opacity duration-300 -z-10"})]})]})]}),(0,r.jsxs)("div",{className:"flex-1 h-full overflow-hidden relative",children:[(0,r.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,107,53,0.1),transparent_50%)]"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,107,53,0.05)_50%,transparent_100%)]"})]}),(0,r.jsx)(c,{executionId:s,events:j,isConnected:v,error:y,isComplete:h})]})]})})}},71848:(e,s,a)=>{a.d(s,{A:()=>i});var r=a(95155),t=a(23405);let l=e=>{let{label:s,value:a}=e;return(0,r.jsxs)("div",{className:"py-2 sm:grid sm:grid-cols-3 sm:gap-4",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-700",children:s}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words",children:null!=a&&""!==a?a:"N/A"})]})},n=e=>{let s,{title:a,data:t}=e;if(null==t)s="N/A";else if("string"==typeof t)s=t;else try{s=JSON.stringify(t,null,2)}catch(e){s="Invalid JSON data"}return(0,r.jsxs)("div",{className:"py-2",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-700 mb-1",children:a}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border",children:(0,r.jsx)("pre",{className:"whitespace-pre-wrap break-all",children:s})})]})};function i(e){var s;let{log:a,onClose:i,apiConfigNameMap:o}=e;if(!a)return null;let d=a.custom_api_config_id?o[a.custom_api_config_id]||"Unknown Model":"N/A";return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out",onClick:i,children:(0,r.jsxs)("div",{className:"card max-w-2xl w-full max-h-[90vh] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Log Details (ID: ",a.id.substring(0,8),"...)"]}),(0,r.jsx)("button",{onClick:i,className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,r.jsx)(t.f,{className:"h-6 w-6"})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-4 overflow-y-auto",children:[(0,r.jsxs)("dl",{className:"divide-y divide-gray-200",children:[(0,r.jsx)(l,{label:"Timestamp",value:new Date(a.request_timestamp).toLocaleString()}),(0,r.jsx)(l,{label:"API Model Used",value:d}),(0,r.jsx)(l,{label:"Role Requested",value:a.role_requested}),(0,r.jsx)(l,{label:"Role Used",value:a.role_used}),(0,r.jsx)(l,{label:"Status",value:null===(s=a.status_code)?(0,r.jsx)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-gray-600 text-gray-100",children:"N/A"}):s>=200&&s<300?(0,r.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-green-600 text-green-100",children:["Success (",s,")"]}):s>=400?(0,r.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-red-600 text-red-100",children:["Error (",s,")"]}):(0,r.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-yellow-500 text-yellow-100",children:["Other (",s,")"]})}),(0,r.jsx)(l,{label:"LLM Provider",value:a.llm_provider_name}),(0,r.jsx)(l,{label:"LLM Model Name",value:a.llm_model_name}),(0,r.jsx)(l,{label:"LLM Latency",value:null!==a.llm_provider_latency_ms?"".concat(a.llm_provider_latency_ms," ms"):"N/A"}),(0,r.jsx)(l,{label:"RoKey Latency",value:null!==a.processing_duration_ms?"".concat(a.processing_duration_ms," ms"):"N/A"}),(0,r.jsx)(l,{label:"Input Tokens",value:null!==a.input_tokens?a.input_tokens:"N/A"}),(0,r.jsx)(l,{label:"Output Tokens",value:null!==a.output_tokens?a.output_tokens:"N/A"}),(0,r.jsx)(l,{label:"Cost",value:null!==a.cost?"$".concat(a.cost.toFixed(6)):"N/A"}),(0,r.jsx)(l,{label:"Multimodal Request",value:a.is_multimodal?"Yes":"No"}),(0,r.jsx)(l,{label:"IP Address",value:a.ip_address}),a.user_id&&(0,r.jsx)(l,{label:"User ID",value:a.user_id}),a.error_message&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l,{label:"Error Message",value:a.error_message}),(0,r.jsx)(l,{label:"Error Source",value:a.error_source})]}),a.llm_provider_status_code&&(0,r.jsx)(l,{label:"LLM Provider Status",value:a.llm_provider_status_code})]}),a.request_payload_summary&&(0,r.jsx)(n,{title:"Request Payload Summary",data:a.request_payload_summary}),a.response_payload_summary&&(0,r.jsx)(n,{title:"Response Payload Summary",data:a.response_payload_summary}),a.error_details_zod&&(0,r.jsx)(n,{title:"Zod Validation Error Details",data:a.error_details_zod})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 text-right",children:(0,r.jsx)("button",{onClick:i,className:"btn-secondary",children:"Close"})})]})})}},74338:(e,s,a)=>{a.d(s,{B0:()=>t,F6:()=>l});var r=a(95155);function t(e){let{className:s=""}=e;return(0,r.jsx)("div",{className:"glass rounded-2xl p-6 animate-pulse ".concat(s),children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}function l(e){let{rows:s=5,columns:a=4}=e;return(0,r.jsx)("div",{className:"glass rounded-2xl overflow-hidden",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-800 p-4 border-b border-gray-700",children:(0,r.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,r.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})}),Array.from({length:s}).map((e,s)=>(0,r.jsx)("div",{className:"p-4 border-b border-gray-700 last:border-b-0",children:(0,r.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,r.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})},s))]})})}},79112:(e,s,a)=>{a.d(s,{A:()=>i});var r=a(95155),t=a(12115);let l=(0,t.lazy)(()=>Promise.all([a.e(5006),a.e(5928),a.e(4726),a.e(4280),a.e(2548),a.e(8960),a.e(8961),a.e(3084),a.e(3285),a.e(274),a.e(3613),a.e(5260),a.e(7525),a.e(3310),a.e(7096),a.e(7455),a.e(678),a.e(8730)]).then(a.bind(a,90882))),n=()=>(0,r.jsxs)("div",{className:"space-y-2 animate-pulse",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]});function i(e){let{content:s,className:a=""}=e;return(0,r.jsx)(t.Suspense,{fallback:(0,r.jsx)(n,{}),children:(0,r.jsx)(l,{content:s,className:a})})}},79958:(e,s,a)=>{a.d(s,{A:()=>t,_:()=>l});var r=a(95155);function t(){return(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-64 animate-pulse"})]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-12 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse"})]})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gray-200 rounded-lg animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-12 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-14 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-18 animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-28 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded-full w-20 animate-pulse"},e))})]})]},e))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-36 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-28 animate-pulse"})]}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-48 animate-pulse"})]}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]},e))})]})]})}function l(){return(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-56 animate-pulse"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]})},e))})]})}a(12115)},80377:(e,s,a)=>{a.d(s,{A:()=>n});var r=a(95155),t=a(12115),l=a(38152);function n(e){let{isOpen:s,onClose:a,onConfirm:n,title:i,message:o,confirmText:d="Delete",cancelText:c="Cancel",type:m="danger",isLoading:x=!1}=e;(0,t.useEffect)(()=>{let e=e=>{"Escape"===e.key&&s&&!x&&a()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,x,a]);let u=(()=>{switch(m){case"danger":return{iconBg:"bg-red-100",iconColor:"text-red-600",confirmButton:"bg-red-600 hover:bg-red-700 focus:ring-red-500",icon:l.uc};case"warning":return{iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmButton:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",icon:l.Pi};default:return{iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmButton:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",icon:l.Pi}}})(),g=u.icon;return s?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300",onClick:x?void 0:a}),(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 scale-100 opacity-100",children:[(0,r.jsx)("div",{className:"relative px-6 pt-6",children:(0,r.jsx)("button",{onClick:a,disabled:x,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)(l.fK,{className:"h-5 w-5"})})}),(0,r.jsxs)("div",{className:"px-6 pb-6",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-6",children:(0,r.jsx)("div",{className:"".concat(u.iconBg," rounded-full p-3"),children:(0,r.jsx)(g,{className:"h-8 w-8 ".concat(u.iconColor)})})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 text-center",children:i}),(0,r.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-8 text-center",children:o}),(0,r.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3",children:[(0,r.jsx)("button",{type:"button",onClick:a,disabled:x,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:c}),(0,r.jsx)("button",{type:"button",onClick:n,disabled:x,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ".concat(u.confirmButton),children:x?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):d})]})]})]})})]}):null}},90882:(e,s,a)=>{a.r(s),a.d(s,{default:()=>d});var r=a(95155),t=a(28831),l=a(70765),n=a(18730),i=a(15478),o=a(95803);function d(e){let{content:s,className:a=""}=e;return(0,r.jsx)("div",{className:"markdown-content ".concat(a),children:(0,r.jsx)(t.Ay,{remarkPlugins:[l.A],components:{h1:e=>{let{children:s}=e;return(0,r.jsx)("h1",{className:"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900",children:s})},h2:e=>{let{children:s}=e;return(0,r.jsx)("h2",{className:"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900",children:s})},h3:e=>{let{children:s}=e;return(0,r.jsx)("h3",{className:"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900",children:s})},h4:e=>{let{children:s}=e;return(0,r.jsx)("h4",{className:"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900",children:s})},p:e=>{let{children:s}=e;return(0,r.jsx)("p",{className:"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words",children:s})},strong:e=>{let{children:s}=e;return(0,r.jsx)("strong",{className:"font-bold text-gray-900",children:s})},em:e=>{let{children:s}=e;return(0,r.jsx)("em",{className:"italic text-gray-900",children:s})},ul:e=>{let{children:s}=e;return(0,r.jsx)("ul",{className:"list-disc list-inside mb-3 space-y-1 text-gray-900",children:s})},ol:e=>{let{children:s}=e;return(0,r.jsx)("ol",{className:"list-decimal list-inside mb-3 space-y-1 text-gray-900",children:s})},li:e=>{let{children:s}=e;return(0,r.jsx)("li",{className:"leading-relaxed text-gray-900",children:s})},code:e=>{let{node:s,inline:a,className:t,children:l,...d}=e,c=/language-(\w+)/.exec(t||""),m=c?c[1]:"",x=String(l).replace(/\n$/,"");if(!a)if(m)return(0,r.jsxs)("div",{className:"my-3 rounded-lg overflow-hidden relative group",children:[(0,r.jsx)("div",{className:"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200",children:(0,r.jsx)(o.A,{text:x,variant:"code",size:"sm",title:"Copy code",className:"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"})}),(0,r.jsx)(n.M,{style:i.bM,language:m,PreTag:"div",className:"text-sm",...d,children:x})]});else return(0,r.jsxs)("div",{className:"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100",children:[(0,r.jsx)("div",{className:"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200",children:(0,r.jsx)(o.A,{text:x,variant:"code",size:"sm",title:"Copy code",className:"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"})}),(0,r.jsx)("pre",{className:"p-4 text-sm font-mono overflow-x-auto",children:(0,r.jsx)("code",{children:x})})]});return(0,r.jsx)("code",{className:"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono",...d,children:l})},blockquote:e=>{let{children:s}=e;return(0,r.jsx)("blockquote",{className:"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700",children:s})},a:e=>{let{children:s,href:a}=e;return(0,r.jsx)("a",{href:a,target:"_blank",rel:"noopener noreferrer",className:"text-orange-600 hover:text-orange-700 underline transition-colors duration-200",children:s})},table:e=>{let{children:s}=e;return(0,r.jsx)("div",{className:"overflow-x-auto my-3",children:(0,r.jsx)("table",{className:"min-w-full border border-gray-200 rounded-lg",children:s})})},thead:e=>{let{children:s}=e;return(0,r.jsx)("thead",{className:"bg-gray-50",children:s})},tbody:e=>{let{children:s}=e;return(0,r.jsx)("tbody",{className:"divide-y divide-gray-200 bg-white",children:s})},tr:e=>{let{children:s}=e;return(0,r.jsx)("tr",{className:"hover:bg-gray-50",children:s})},th:e=>{let{children:s}=e;return(0,r.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200",children:s})},td:e=>{let{children:s}=e;return(0,r.jsx)("td",{className:"px-3 py-2 text-sm text-gray-900 border-b border-gray-200",children:s})},hr:()=>(0,r.jsx)("hr",{className:"my-4 border-gray-200"})},children:s})})}},95565:(e,s,a)=>{a.d(s,{AnalyticsSkeleton:()=>d,ConfigSelectorSkeleton:()=>n,MessageSkeleton:()=>l,MyModelsSkeleton:()=>i,RoutingSetupSkeleton:()=>o});var r=a(95155);a(11518),a(12115);let t=e=>{let{className:s="",variant:a="text",width:t="100%",height:l="1rem",lines:n=1}=e,i="animate-pulse bg-gray-200 rounded",o=()=>{switch(a){case"circular":return"rounded-full";case"rectangular":return"rounded-lg";default:return"rounded"}},d={width:"number"==typeof t?"".concat(t,"px"):t,height:"number"==typeof l?"".concat(l,"px"):l};return n>1?(0,r.jsx)("div",{className:"space-y-2 ".concat(s),children:Array.from({length:n}).map((e,s)=>(0,r.jsx)("div",{className:"".concat(i," ").concat(o()),style:{...d,width:s===n-1?"75%":d.width}},s))}):(0,r.jsx)("div",{className:"".concat(i," ").concat(o()," ").concat(s),style:d})},l=()=>(0,r.jsx)("div",{className:"space-y-6 py-8",children:Array.from({length:3}).map((e,s)=>(0,r.jsx)("div",{className:"flex ".concat(s%2==0?"justify-end":"justify-start"),children:(0,r.jsx)("div",{className:"max-w-3xl p-4 rounded-2xl ".concat(s%2==0?"bg-orange-50":"bg-white border border-gray-200"),children:(0,r.jsx)(t,{lines:3,height:"1rem"})})},s))}),n=()=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(t,{variant:"circular",width:32,height:32}),(0,r.jsx)(t,{width:"8rem",height:"1.5rem"})]}),i=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(t,{height:"2.5rem",width:"10rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1.25rem",width:"18rem"})]}),(0,r.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"10rem"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,s)=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(t,{height:"1.5rem",width:"8rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1rem",width:"12rem"})]}),(0,r.jsx)(t,{variant:"circular",width:32,height:32})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(t,{height:"0.875rem",width:"4rem"}),(0,r.jsx)(t,{height:"0.875rem",width:"2rem"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(t,{height:"0.875rem",width:"5rem"}),(0,r.jsx)(t,{height:"0.875rem",width:"3rem"})]})]})]},s))})]}),o=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(t,{height:"3rem",width:"16rem",className:"mx-auto mb-4"}),(0,r.jsx)(t,{height:"1.25rem",width:"24rem",className:"mx-auto"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:Array.from({length:4}).map((e,s)=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(t,{variant:"circular",width:48,height:48,className:"mr-4"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(t,{height:"1.5rem",width:"10rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1rem",width:"8rem"})]})]}),(0,r.jsx)(t,{lines:3,height:"0.875rem"})]},s))})]}),d=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(t,{height:"2.5rem",width:"9rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1.25rem",width:"18rem"})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"8rem"}),(0,r.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"6rem"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,s)=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(t,{height:"1.25rem",width:"6rem",className:"mb-4"}),(0,r.jsx)(t,{height:"2.5rem",width:"5rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1rem",width:"8rem"})]},s))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(t,{height:"1.5rem",width:"12rem",className:"mb-6"}),(0,r.jsx)(t,{variant:"rectangular",height:"24rem"})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(t,{height:"1.5rem",width:"10rem",className:"mb-6"}),(0,r.jsx)(t,{variant:"rectangular",height:"24rem"})]})]})]})},95803:(e,s,a)=>{a.d(s,{A:()=>n});var r=a(95155),t=a(12115),l=a(76032);function n(e){let{text:s,className:a="",size:n="sm",variant:i="default",title:o="Copy to clipboard"}=e,[d,c]=(0,t.useState)(!1),m=async()=>{try{await navigator.clipboard.writeText(s),c(!0),setTimeout(()=>c(!1),2e3)}catch(a){let e=document.createElement("textarea");e.value=s,document.body.appendChild(e),e.focus(),e.select();try{document.execCommand("copy"),c(!0),setTimeout(()=>c(!1),2e3)}catch(e){}document.body.removeChild(e)}},x={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"};return(0,r.jsx)("button",{onClick:m,className:"\n        ".concat({sm:"p-1.5",md:"p-2",lg:"p-2.5"}[n],"\n        ").concat({default:"text-gray-500 hover:text-gray-700 hover:bg-gray-100/80",code:"text-gray-300 hover:text-white hover:bg-gray-600/80",message:"text-gray-500 hover:text-gray-700 hover:bg-white/20"}[i],"\n        rounded transition-all duration-200 cursor-pointer\n        ").concat(d?"text-green-600":"","\n        ").concat(a,"\n      "),title:d?"Copied!":o,children:d?(0,r.jsx)(l.S,{className:"".concat(x[n]," stroke-2")}):(0,r.jsx)(l.X,{className:"".concat(x[n]," stroke-2")})})}},99030:(e,s,a)=>{a.d(s,{default:()=>l});var r=a(12115),t=a(34962);function l(){let{pageTitle:e}=(0,t.rT)();return(0,r.useEffect)(()=>{"undefined"!=typeof document&&(document.title=e)},[e]),null}}}]);