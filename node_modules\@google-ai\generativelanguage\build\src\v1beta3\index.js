"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextServiceClient = exports.PermissionServiceClient = exports.ModelServiceClient = exports.DiscussServiceClient = void 0;
var discuss_service_client_1 = require("./discuss_service_client");
Object.defineProperty(exports, "DiscussServiceClient", { enumerable: true, get: function () { return discuss_service_client_1.DiscussServiceClient; } });
var model_service_client_1 = require("./model_service_client");
Object.defineProperty(exports, "ModelServiceClient", { enumerable: true, get: function () { return model_service_client_1.ModelServiceClient; } });
var permission_service_client_1 = require("./permission_service_client");
Object.defineProperty(exports, "PermissionServiceClient", { enumerable: true, get: function () { return permission_service_client_1.PermissionServiceClient; } });
var text_service_client_1 = require("./text_service_client");
Object.defineProperty(exports, "TextServiceClient", { enumerable: true, get: function () { return text_service_client_1.TextServiceClient; } });
//# sourceMappingURL=index.js.map