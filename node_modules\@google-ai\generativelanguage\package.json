{"name": "@google-ai/generativelanguage", "version": "3.2.0", "description": "Generative Language API client for Node.js", "repository": {"type": "git", "url": "https://github.com/googleapis/google-cloud-node.git", "directory": "packages/google-ai-generativelanguage"}, "license": "Apache-2.0", "author": "Google LLC", "main": "build/src/index.js", "files": ["build/src", "build/protos"], "homepage": "https://github.com/googleapis/google-cloud-node/tree/main/packages/google-ai-generativelanguage", "keywords": ["google apis client", "google api client", "google apis", "google api", "google", "google cloud platform", "google cloud", "cloud", "google generativelanguage", "generativelanguage", "Generative Language API"], "scripts": {"clean": "gts clean", "compile": "tsc -p . && cp -r protos build/", "compile-protos": "compileProtos src", "docs": "jsdoc -c .jsdoc.js", "predocs-test": "npm run docs", "docs-test": "linkinator docs", "fix": "gts fix", "lint": "gts check", "prepare": "npm run compile", "system-test": "npm run compile && c8 mocha build/system-test", "test": "c8 mocha build/test", "samples-test": "npm run compile && cd samples/ && npm link ../ && npm i && npm test", "prelint": "cd samples; npm link ../; npm i"}, "dependencies": {"google-gax": "^5.0.1-rc.0"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^22.13.9", "@types/sinon": "^17.0.4", "c8": "^10.1.3", "gapic-tools": "^1.0.0", "gts": "^6.0.2", "jsdoc": "^4.0.4", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^6.1.2", "long": "^5.3.1", "mocha": "^11.1.0", "pack-n-play": "^3.0.0", "sinon": "^19.0.2", "typescript": "^5.8.2"}, "engines": {"node": ">=18"}}