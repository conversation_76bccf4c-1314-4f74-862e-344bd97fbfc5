'use client';

import { useState, useCallback, useRef } from 'react';

interface RoutingSetupData {
  configDetails: any;
  apiKeys: any[];
  routingStrategy: string;
  routingParams: any;
  complexityAssignments: any[];
}

interface PrefetchCache {
  [configId: string]: {
    data: RoutingSetupData;
    timestamp: number;
    isLoading: boolean;
  };
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const prefetchCache: PrefetchCache = {};

export function useRoutingSetupPrefetch() {
  const [prefetchStatus, setPrefetchStatus] = useState<{
    [configId: string]: 'idle' | 'loading' | 'success' | 'error';
  }>({});

  const abortControllers = useRef<{ [configId: string]: AbortController }>({});

  // Check if data is cached and fresh
  const isCached = useCallback((configId: string) => {
    const cached = prefetchCache[configId];
    if (!cached) return false;
    
    const isExpired = Date.now() - cached.timestamp > CACHE_DURATION;
    return !isExpired && !cached.isLoading;
  }, []);

  // Get cached data
  const getCachedData = useCallback((configId: string): RoutingSetupData | null => {
    const cached = prefetchCache[configId];
    if (!cached || cached.isLoading) return null;
    
    const isExpired = Date.now() - cached.timestamp > CACHE_DURATION;
    if (isExpired) {
      delete prefetchCache[configId];
      return null;
    }
    
    return cached.data;
  }, []);

  // Prefetch all data needed for routing setup page
  const prefetchRoutingSetupData = useCallback(async (configId: string, priority: 'high' | 'medium' | 'low' = 'medium') => {
    // Check if already cached
    if (isCached(configId)) {
      console.log(`🚀 [ROUTING SETUP PREFETCH] Using cached data for config: ${configId}`);
      return getCachedData(configId);
    }

    // Check if already loading
    if (prefetchCache[configId]?.isLoading) {
      console.log(`🔄 [ROUTING SETUP PREFETCH] Already loading config: ${configId}`);
      return null;
    }

    // Cancel any existing request for this config
    if (abortControllers.current[configId]) {
      abortControllers.current[configId].abort();
    }

    // Create new abort controller
    const abortController = new AbortController();
    abortControllers.current[configId] = abortController;

    // Set loading state
    prefetchCache[configId] = {
      data: {} as RoutingSetupData,
      timestamp: Date.now(),
      isLoading: true
    };

    setPrefetchStatus(prev => ({ ...prev, [configId]: 'loading' }));

    try {
      console.log(`🚀 [ROUTING SETUP PREFETCH] Starting prefetch for config: ${configId} (priority: ${priority})`);

      // Add delay for lower priority requests
      if (priority === 'low') {
        await new Promise(resolve => setTimeout(resolve, 200));
      } else if (priority === 'medium') {
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Fetch all data in parallel
      const [
        configResponse,
        keysResponse,
        complexityAssignmentsResponse
      ] = await Promise.allSettled([
        fetch(`/api/custom-configs/${configId}`, { signal: abortController.signal }),
        fetch(`/api/keys?custom_config_id=${configId}`, { signal: abortController.signal }),
        fetch(`/api/complexity-assignments?custom_config_id=${configId}`, { signal: abortController.signal })
      ]);

      // Process responses
      let configDetails = null;
      let apiKeys: any[] = [];
      let routingStrategy = 'none';
      let routingParams = {};
      let complexityAssignments: any[] = [];

      // Process config details
      if (configResponse.status === 'fulfilled' && configResponse.value.ok) {
        configDetails = await configResponse.value.json();
        routingStrategy = configDetails.routing_strategy || 'none';
        routingParams = configDetails.routing_strategy_params || {};
      }

      // Process API keys
      if (keysResponse.status === 'fulfilled' && keysResponse.value.ok) {
        apiKeys = await keysResponse.value.json();
      }

      // Process complexity assignments
      if (complexityAssignmentsResponse.status === 'fulfilled' && complexityAssignmentsResponse.value.ok) {
        complexityAssignments = await complexityAssignmentsResponse.value.json();
      }

      const prefetchedData: RoutingSetupData = {
        configDetails,
        apiKeys,
        routingStrategy,
        routingParams,
        complexityAssignments
      };

      // Cache the data
      prefetchCache[configId] = {
        data: prefetchedData,
        timestamp: Date.now(),
        isLoading: false
      };

      setPrefetchStatus(prev => ({ ...prev, [configId]: 'success' }));

      console.log(`✅ [ROUTING SETUP PREFETCH] Successfully prefetched data for config: ${configId}`, {
        configFound: !!configDetails,
        keysCount: apiKeys.length,
        strategy: routingStrategy,
        hasParams: Object.keys(routingParams).length > 0,
        complexityAssignmentsCount: complexityAssignments.length
      });

      return prefetchedData;

    } catch (error: Error) {
      if (error.name === 'AbortError') {
        console.log(`🚫 [ROUTING SETUP PREFETCH] Aborted prefetch for config: ${configId}`);
        return null;
      }

      console.error(`❌ [ROUTING SETUP PREFETCH] Failed to prefetch data for config: ${configId}`, error);
      
      // Remove from cache on error
      delete prefetchCache[configId];
      setPrefetchStatus(prev => ({ ...prev, [configId]: 'error' }));
      
      return null;
    } finally {
      // Clean up abort controller
      delete abortControllers.current[configId];
    }
  }, [isCached, getCachedData]);

  // Prefetch on hover
  const createHoverPrefetch = useCallback((configId: string) => {
    return {
      onMouseEnter: () => {
        if (!isCached(configId)) {
          prefetchRoutingSetupData(configId, 'high');
        }
      }
    };
  }, [prefetchRoutingSetupData, isCached]);

  // Clear cache for a specific config
  const clearCache = useCallback((configId: string) => {
    delete prefetchCache[configId];
    setPrefetchStatus(prev => {
      const newStatus = { ...prev };
      delete newStatus[configId];
      return newStatus;
    });
  }, []);

  // Clear all cache
  const clearAllCache = useCallback(() => {
    Object.keys(prefetchCache).forEach(configId => {
      delete prefetchCache[configId];
    });
    setPrefetchStatus({});
  }, []);

  // Get prefetch status
  const getStatus = useCallback((configId: string) => {
    return prefetchStatus[configId] || 'idle';
  }, [prefetchStatus]);

  // Get cache info
  const getCacheInfo = useCallback(() => {
    return {
      cachedConfigs: Object.keys(prefetchCache),
      cacheSize: Object.keys(prefetchCache).length,
      totalCacheAge: Object.values(prefetchCache).reduce((total, cache) => {
        return total + (Date.now() - cache.timestamp);
      }, 0) / Object.keys(prefetchCache).length
    };
  }, []);

  return {
    prefetchRoutingSetupData,
    getCachedData,
    isCached,
    createHoverPrefetch,
    clearCache,
    clearAllCache,
    getStatus,
    getCacheInfo,
    prefetchStatus
  };
}
