{"interfaces": {"google.ai.generativelanguage.v1alpha.GenerativeService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"GenerateContent": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GenerateAnswer": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "StreamGenerateContent": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "EmbedContent": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchEmbedContents": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CountTokens": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BidiGenerateContent": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}