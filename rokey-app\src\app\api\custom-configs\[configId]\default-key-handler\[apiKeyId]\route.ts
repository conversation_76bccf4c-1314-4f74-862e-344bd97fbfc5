import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

interface RouteParams {
  params: Promise<{
    configId: string;
    apiKeyId: string; 
  }>;
}

// PUT /api/custom-configs/:configId/default-key-handler/:apiKeyId
export async function PUT(request: NextRequest, { params }: RouteParams) {
  console.log('NEW_PATH Reached PUT /api/custom-configs/:configId/default-key-handler/:apiKeyId with params:', params);
  const supabase = await createSupabaseServerClientOnRequest();
  const { configId, apiKeyId } = await params;

  if (!configId || !apiKeyId) {
    return NextResponse.json({ error: 'Configuration ID and API Key ID are required' }, { status: 400 });
  }

  try {
    const { data: apiKeyToCheck, error: checkError } = await supabase
      .from('api_keys')
      .select('id, custom_api_config_id')
      .eq('id', apiKeyId)
      .eq('custom_api_config_id', configId)
      .single();

    if (checkError || !apiKeyToCheck) {
      console.error('Error verifying API key ownership or key not found (new path):', checkError);
      return NextResponse.json({ error: 'API Key not found in the specified configuration or error verifying.' }, { status: 404 });
    }

    const { error: updateOthersError } = await supabase
      .from('api_keys')
      .update({ is_default_general_chat_model: false, updated_at: new Date().toISOString() })
      .eq('custom_api_config_id', configId)
      .neq('id', apiKeyId);

    if (updateOthersError) {
      console.error('Supabase error unsetting other default keys (new path):', updateOthersError);
      return NextResponse.json({ error: 'Failed to unset other default keys', details: updateOthersError.message }, { status: 500 });
    }

    const { data: updatedKey, error: setKeyError } = await supabase
      .from('api_keys')
      .update({ is_default_general_chat_model: true, updated_at: new Date().toISOString() })
      .eq('id', apiKeyId)
      .eq('custom_api_config_id', configId)
      .select('id, label, is_default_general_chat_model')
      .single();

    if (setKeyError) {
      console.error('Supabase error setting default key (new path):', setKeyError);
      if (setKeyError.code === '23505') { 
         return NextResponse.json({ error: 'Failed to set default key. Another key might already be set as default, or an issue with unsetting previous default.', details: setKeyError.message }, { status: 409 });
      }
      return NextResponse.json({ error: 'Failed to set API key as default general chat model', details: setKeyError.message }, { status: 500 });
    }

    return NextResponse.json({ 
        message: 'API key successfully set as default general chat model (new path).', 
        data: updatedKey 
    }, { status: 200 });

  } catch (e: any) {
    console.error('Error in PUT /api/custom-configs/:configId/default-key-handler/:apiKeyId:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
} 