import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

// DELETE /api/chat/messages/delete-after-timestamp
// Deletes messages after a specific timestamp for conversation cleanup during edits/retries
export async function DELETE(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();

  try {
    const requestData = await request.json();
    const { conversation_id, after_timestamp, from_timestamp } = requestData;

    if (!conversation_id) {
      return NextResponse.json({ error: 'conversation_id is required' }, { status: 400 });
    }

    if (!after_timestamp && !from_timestamp) {
      return NextResponse.json({ error: 'Either after_timestamp or from_timestamp is required' }, { status: 400 });
    }

    // Convert timestamp to date for database comparison
    const timestampToUse = after_timestamp || from_timestamp;
    const targetDate = new Date(parseInt(timestampToUse));
    
    if (isNaN(targetDate.getTime())) {
      return NextResponse.json({ error: 'Invalid timestamp format' }, { status: 400 });
    }

    console.log(`🗑️ [DELETE-TIMESTAMP] Deleting messages ${after_timestamp ? 'after' : 'from'} timestamp: ${timestampToUse} (${targetDate.toISOString()}) in conversation: ${conversation_id}`);

    // Build the delete query
    let deleteQuery = supabase
      .from('chat_messages')
      .delete()
      .eq('conversation_id', conversation_id);

    if (after_timestamp) {
      // Delete messages created after the timestamp (for edit operations)
      deleteQuery = deleteQuery.gt('created_at', targetDate.toISOString());
    } else {
      // Delete messages created from the timestamp onwards (for retry operations)
      deleteQuery = deleteQuery.gte('created_at', targetDate.toISOString());
    }

    const { data, error, count } = await deleteQuery.select('id');

    if (error) {
      console.error('Supabase error deleting messages:', error);
      return NextResponse.json({ 
        error: 'Failed to delete messages', 
        details: error.message 
      }, { status: 500 });
    }

    const deletedCount = data?.length || 0;
    console.log(`✅ [DELETE-TIMESTAMP] Successfully deleted ${deletedCount} messages`);

    // Update conversation's updated_at timestamp
    await supabase
      .from('chat_conversations')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', conversation_id);

    return NextResponse.json({ 
      success: true, 
      deleted_count: deletedCount,
      message: `Deleted ${deletedCount} messages ${after_timestamp ? 'after' : 'from'} timestamp ${timestampToUse}`
    }, { status: 200 });

  } catch (e: Error) {
    console.error('Error in DELETE /api/chat/messages/delete-after-timestamp:', e);
    if (e.name === 'SyntaxError') {
      return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    return NextResponse.json({ 
      error: 'An unexpected error occurred', 
      details: e.message 
    }, { status: 500 });
  }
}
