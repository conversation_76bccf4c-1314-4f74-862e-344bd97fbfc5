#!/usr/bin/env node

/**
 * Automated ESLint Fix Script for RouKey
 * 
 * This script automatically fixes common ESLint warnings that can be safely automated:
 * - Removes unused imports and variables
 * - Converts 'let' to 'const' where appropriate
 * - Fixes React unescaped entities
 * - Replaces 'any' types with more specific types where possible
 * - Removes unused function parameters
 * 
 * Usage: node scripts/fix-eslint.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const SRC_DIR = path.join(__dirname, '../src');
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];
const BACKUP_DIR = path.join(__dirname, '../.eslint-fixes-backup');

// Common type replacements for 'any'
const TYPE_REPLACEMENTS = {
  'err: any': 'err: Error',
  'error: any': 'error: Error',
  'e: any': 'e: Error',
  'data: any': 'data: unknown',
  'response: any': 'response: unknown',
  'result: any': 'result: unknown',
  'item: any': 'item: unknown',
  'value: any': 'value: unknown',
  'config: any': 'config: Record<string, unknown>',
  'params: any': 'params: Record<string, unknown>',
  'body: any': 'body: Record<string, unknown>',
  'req: any': 'req: unknown',
  'res: any': 'res: unknown'
};

// HTML entity replacements
const HTML_ENTITIES = {
  '"': '&quot;',
  "'": '&#x27;',
  '<': '&lt;',
  '>': '&gt;',
  '&': '&amp;'
};

class ESLintFixer {
  constructor() {
    this.fixedFiles = [];
    this.errors = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`[${timestamp}] ${prefix} ${message}`);
  }

  createBackup() {
    this.log('Creating backup of source files...');
    if (fs.existsSync(BACKUP_DIR)) {
      fs.rmSync(BACKUP_DIR, { recursive: true });
    }
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
    
    try {
      execSync(`xcopy "${SRC_DIR}" "${BACKUP_DIR}" /E /I /H /Y`, { stdio: 'ignore' });
      this.log('Backup created successfully', 'success');
    } catch (error) {
      // Try with robocopy on Windows or cp on Unix
      try {
        execSync(`robocopy "${SRC_DIR}" "${BACKUP_DIR}" /E`, { stdio: 'ignore' });
        this.log('Backup created successfully', 'success');
      } catch (error2) {
        this.log('Warning: Could not create backup', 'error');
      }
    }
  }

  getAllFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        this.getAllFiles(filePath, fileList);
      } else if (EXTENSIONS.includes(path.extname(file))) {
        fileList.push(filePath);
      }
    });
    
    return fileList;
  }

  fixUnusedImports(content) {
    // Very conservative - only remove obviously unused single imports
    let fixed = content;

    // Only remove imports that are clearly unused (simple cases)
    const lines = content.split('\n');
    const newLines = [];

    for (const line of lines) {
      // Skip empty import lines like: import { } from 'module'
      if (line.match(/^\s*import\s*{\s*}\s*from/)) {
        continue;
      }
      newLines.push(line);
    }

    return newLines.join('\n');
  }

  fixPreferConst(content) {
    // Very conservative - only fix obvious cases
    let fixed = content;

    // Only fix simple, obvious cases where let is clearly not reassigned
    // Pattern: let variable = value; (at end of line, no complex expressions)
    fixed = fixed.replace(/(\s+)let\s+(\w+)\s*=\s*([^;=]+);(\s*$)/gm, (match, indent, varName, value, ending) => {
      // Only if it's a simple assignment (no complex expressions)
      if (value.includes('(') || value.includes('[') || value.includes('{')) {
        return match; // Skip complex expressions
      }
      return `${indent}const ${varName} = ${value};${ending}`;
    });

    return fixed;
  }

  fixAnyTypes(content) {
    // Very conservative - only fix the most obvious cases
    let fixed = content;

    // Only fix very safe, common patterns
    fixed = fixed.replace(/catch\s*\(\s*err:\s*any\s*\)/g, 'catch (err: Error)');
    fixed = fixed.replace(/catch\s*\(\s*error:\s*any\s*\)/g, 'catch (error: Error)');
    fixed = fixed.replace(/catch\s*\(\s*e:\s*any\s*\)/g, 'catch (e: Error)');

    return fixed;
  }

  fixUnusedVars(content) {
    // Very conservative - only prefix obviously unused parameters
    let fixed = content;

    // Only fix simple unused parameters in arrow functions
    // Pattern: (param) => or (param: type) => where param is clearly unused
    fixed = fixed.replace(/\(\s*([a-z]+)\s*\)\s*=>/g, '(_$1) =>');
    fixed = fixed.replace(/\(\s*([a-z]+):\s*\w+\s*\)\s*=>/g, '(_$1: unknown) =>');

    return fixed;
  }

  fixHtmlEntities(content) {
    // Very conservative - only fix obvious cases
    let fixed = content;

    // Only fix quotes in JSX text content (very conservative)
    fixed = fixed.replace(/>([^<]*"[^<]*)</g, (match, text) => {
      return `>${text.replace(/"/g, '&quot;')}<`;
    });

    fixed = fixed.replace(/>([^<]*'[^<]*)</g, (match, text) => {
      return `>${text.replace(/'/g, '&#x27;')}<`;
    });

    return fixed;
  }

  fixFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;

      // Apply only the safest fixes
      content = this.fixUnusedImports(content);
      content = this.fixAnyTypes(content);

      // Only write if content changed
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.fixedFiles.push(filePath);
        return true;
      }

      return false;
    } catch (error) {
      this.errors.push({ file: filePath, error: error.message });
      return false;
    }
  }

  async run() {
    this.log('🚀 Starting automated ESLint fixes...');
    
    // Create backup
    this.createBackup();
    
    // Get all files
    const files = this.getAllFiles(SRC_DIR);
    this.log(`Found ${files.length} files to process`);
    
    // Process files
    let fixedCount = 0;
    files.forEach(file => {
      if (this.fixFile(file)) {
        fixedCount++;
        this.log(`Fixed: ${path.relative(SRC_DIR, file)}`);
      }
    });
    
    // Run ESLint --fix for additional automatic fixes
    this.log('Running ESLint --fix for additional automatic fixes...');
    try {
      execSync('npm run lint -- --fix', { stdio: 'inherit', cwd: path.dirname(SRC_DIR) });
    } catch (error) {
      this.log('ESLint --fix completed with warnings (this is normal)', 'info');
    }
    
    // Summary
    this.log(`\n📊 Summary:`);
    this.log(`✅ Files processed: ${files.length}`);
    this.log(`✅ Files fixed: ${fixedCount}`);
    this.log(`❌ Errors: ${this.errors.length}`);
    
    if (this.errors.length > 0) {
      this.log('\n❌ Errors encountered:');
      this.errors.forEach(({ file, error }) => {
        this.log(`  ${path.relative(SRC_DIR, file)}: ${error}`, 'error');
      });
    }
    
    this.log('\n🎉 Automated fixes complete!');
    this.log(`💾 Backup saved to: ${BACKUP_DIR}`);
    this.log('🔍 Run "npm run lint" to see remaining warnings');
  }
}

// Run the fixer
if (require.main === module) {
  const fixer = new ESLintFixer();
  fixer.run().catch(console.error);
}

module.exports = ESLintFixer;
