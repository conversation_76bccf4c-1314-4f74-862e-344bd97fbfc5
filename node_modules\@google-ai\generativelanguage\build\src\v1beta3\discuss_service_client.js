"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiscussServiceClient = void 0;
const jsonProtos = require("../../protos/protos.json");
const google_gax_1 = require("google-gax");
/**
 * Client JSON configuration object, loaded from
 * `src/v1beta3/discuss_service_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./discuss_service_client_config.json");
const version = require('../../../package.json').version;
/**
 *  An API for using Generative Language Models (GLMs) in dialog applications.
 *
 *  Also known as large language models (LLMs), this API provides models that
 *  are trained for multi-turn dialog.
 * @class
 * @memberof v1beta3
 */
class DiscussServiceClient {
    _terminated = false;
    _opts;
    _providedCustomServicePath;
    _gaxModule;
    _gaxGrpc;
    _protos;
    _defaults;
    _universeDomain;
    _servicePath;
    _log = google_gax_1.loggingUtils.log('generativelanguage');
    auth;
    descriptors = {
        page: {},
        stream: {},
        longrunning: {},
        batching: {},
    };
    warn;
    innerApiCalls;
    pathTemplates;
    discussServiceStub;
    /**
     * Construct an instance of DiscussServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new DiscussServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if (opts?.universe_domain &&
            opts?.universeDomain &&
            opts?.universe_domain !== opts?.universeDomain) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            opts?.universeDomain ??
                opts?.universe_domain ??
                universeDomainEnvVar ??
                'googleapis.com';
        this._servicePath = 'generativelanguage.' + this._universeDomain;
        const servicePath = opts?.servicePath || opts?.apiEndpoint || this._servicePath;
        this._providedCustomServicePath = !!(opts?.servicePath || opts?.apiEndpoint);
        const port = opts?.port || staticMembers.port;
        const clientConfig = opts?.clientConfig ?? {};
        const fallback = opts?.fallback ??
            (typeof window !== 'undefined' && typeof window?.fetch === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            modelPathTemplate: new this._gaxModule.PathTemplate('models/{model}'),
            permissionPathTemplate: new this._gaxModule.PathTemplate('tunedModels/{tuned_model}/permissions/{permission}'),
            tunedModelPathTemplate: new this._gaxModule.PathTemplate('tunedModels/{tuned_model}'),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.ai.generativelanguage.v1beta3.DiscussService', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.discussServiceStub) {
            return this.discussServiceStub;
        }
        // Put together the "service stub" for
        // google.ai.generativelanguage.v1beta3.DiscussService.
        this.discussServiceStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.ai.generativelanguage.v1beta3.DiscussService')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.ai.generativelanguage.v1beta3
                    .DiscussService, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const discussServiceStubMethods = ['generateMessage', 'countMessageTokens'];
        for (const methodName of discussServiceStubMethods) {
            const callPromise = this.discussServiceStub.then(stub => (...args) => {
                if (this._terminated) {
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.discussServiceStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'generativelanguage.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'generativelanguage.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return [];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    generateMessage(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                model: request.model ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('generateMessage request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('generateMessage response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .generateMessage(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('generateMessage response %j', response);
            return [response, options, rawResponse];
        });
    }
    countMessageTokens(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                model: request.model ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('countMessageTokens request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('countMessageTokens response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .countMessageTokens(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('countMessageTokens response %j', response);
            return [response, options, rawResponse];
        });
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(model) {
        return this.pathTemplates.modelPathTemplate.render({
            model: model,
        });
    }
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).model;
    }
    /**
     * Return a fully-qualified permission resource name string.
     *
     * @param {string} tuned_model
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    permissionPath(tunedModel, permission) {
        return this.pathTemplates.permissionPathTemplate.render({
            tuned_model: tunedModel,
            permission: permission,
        });
    }
    /**
     * Parse the tuned_model from Permission resource.
     *
     * @param {string} permissionName
     *   A fully-qualified path representing Permission resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromPermissionName(permissionName) {
        return this.pathTemplates.permissionPathTemplate.match(permissionName)
            .tuned_model;
    }
    /**
     * Parse the permission from Permission resource.
     *
     * @param {string} permissionName
     *   A fully-qualified path representing Permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromPermissionName(permissionName) {
        return this.pathTemplates.permissionPathTemplate.match(permissionName)
            .permission;
    }
    /**
     * Return a fully-qualified tunedModel resource name string.
     *
     * @param {string} tuned_model
     * @returns {string} Resource name string.
     */
    tunedModelPath(tunedModel) {
        return this.pathTemplates.tunedModelPathTemplate.render({
            tuned_model: tunedModel,
        });
    }
    /**
     * Parse the tuned_model from TunedModel resource.
     *
     * @param {string} tunedModelName
     *   A fully-qualified path representing TunedModel resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelName(tunedModelName) {
        return this.pathTemplates.tunedModelPathTemplate.match(tunedModelName)
            .tuned_model;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.discussServiceStub && !this._terminated) {
            return this.discussServiceStub.then(stub => {
                this._log.info('ending gRPC channel');
                this._terminated = true;
                stub.close();
            });
        }
        return Promise.resolve();
    }
}
exports.DiscussServiceClient = DiscussServiceClient;
//# sourceMappingURL=discuss_service_client.js.map