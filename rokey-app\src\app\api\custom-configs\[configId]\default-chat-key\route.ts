import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

interface RouteParams {
  params: Promise<{
    configId: string;
  }>;
}

// GET /api/custom-configs/:configId/default-chat-key
// Retrieves the API key designated as the default general chat model for a specific configuration.
export async function GET(request: NextRequest, { params }: RouteParams) {
  const supabase = await createSupabaseServerClientOnRequest();
  const { configId } = await params;

  if (!configId) {
    return NextResponse.json({ error: 'Configuration ID is required' }, { status: 400 });
  }

  // TODO: Milestone 13: Add user authentication check
  // Ensure the user owns this configId or has rights to view it.

  try {
    const { data: defaultKey, error } = await supabase
      .from('api_keys')
      .select('id, label, provider, predefined_model_id, status, custom_api_config_id, is_default_general_chat_model') // Select fields consistent with DisplayApiKey or what's needed
      .eq('custom_api_config_id', configId)
      .eq('is_default_general_chat_model', true)
      .maybeSingle(); // Use maybeSingle() as it's possible no default is set

    if (error) {
      console.error('Supabase error fetching default chat key:', error);
      return NextResponse.json({ error: 'Failed to fetch default chat key', details: error.message }, { status: 500 });
    }

    if (!defaultKey) {
      // This is not an error, it just means no default key is set for this config.
      // Frontend handles this by not marking any key as default.
      return NextResponse.json(null, { status: 200 }); // Or return 404 if specifically required by frontend logic, but 200 with null is also fine.
    }

    return NextResponse.json(defaultKey, { status: 200 });

  } catch (e: any) {
    console.error('Error in GET /api/custom-configs/:configId/default-chat-key:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
} 