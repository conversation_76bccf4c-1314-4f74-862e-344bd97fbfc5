// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ai.generativelanguage.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/ai/generativelanguage/apiv1beta/generativelanguagepb;generativelanguagepb";
option java_multiple_files = true;
option java_outer_classname = "FileProto";
option java_package = "com.google.ai.generativelanguage.v1beta";

// A file uploaded to the API.
// Next ID: 15
message File {
  option (google.api.resource) = {
    type: "generativelanguage.googleapis.com/File"
    pattern: "files/{file}"
    plural: "files"
    singular: "file"
  };

  // States for the lifecycle of a File.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // File is being processed and cannot be used for inference yet.
    PROCESSING = 1;

    // File is processed and available for inference.
    ACTIVE = 2;

    // File failed processing.
    FAILED = 10;
  }

  enum Source {
    // Used if source is not specified.
    SOURCE_UNSPECIFIED = 0;

    // Indicates the file is uploaded by the user.
    UPLOADED = 1;

    // Indicates the file is generated by Google.
    GENERATED = 2;
  }

  // Metadata for the File.
  oneof metadata {
    // Output only. Metadata for a video.
    VideoMetadata video_metadata = 12
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Immutable. Identifier. The `File` resource name. The ID (name excluding the
  // "files/" prefix) can contain up to 40 characters that are lowercase
  // alphanumeric or dashes (-). The ID cannot start or end with a dash. If the
  // name is empty on create, a unique name will be generated. Example:
  // `files/123-456`
  string name = 1 [
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. The human-readable display name for the `File`. The display name
  // must be no more than 512 characters in length, including spaces. Example:
  // "Welcome Image"
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. MIME type of the file.
  string mime_type = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Size of the file in bytes.
  int64 size_bytes = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp of when the `File` was created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp of when the `File` was last updated.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp of when the `File` will be deleted. Only set if
  // the `File` is scheduled to expire.
  google.protobuf.Timestamp expiration_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. SHA-256 hash of the uploaded bytes.
  bytes sha256_hash = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The uri of the `File`.
  string uri = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The download uri of the `File`.
  string download_uri = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Processing state of the File.
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Source of the File.
  Source source = 13;

  // Output only. Error status if File processing failed.
  google.rpc.Status error = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata for a video `File`.
message VideoMetadata {
  // Duration of the video.
  google.protobuf.Duration video_duration = 1;
}
