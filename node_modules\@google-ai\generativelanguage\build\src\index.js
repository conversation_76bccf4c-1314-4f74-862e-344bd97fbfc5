"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by synthtool. **
// ** https://github.com/googleapis/synthtool **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.protos = exports.TextServiceClient = exports.RetrieverServiceClient = exports.PredictionServiceClient = exports.PermissionServiceClient = exports.ModelServiceClient = exports.GenerativeServiceClient = exports.FileServiceClient = exports.DiscussServiceClient = exports.CacheServiceClient = exports.v1beta3 = exports.v1beta2 = exports.v1beta = exports.v1alpha = exports.v1 = void 0;
const v1 = require("./v1");
exports.v1 = v1;
const v1alpha = require("./v1alpha");
exports.v1alpha = v1alpha;
const v1beta = require("./v1beta");
exports.v1beta = v1beta;
const v1beta2 = require("./v1beta2");
exports.v1beta2 = v1beta2;
const v1beta3 = require("./v1beta3");
exports.v1beta3 = v1beta3;
const CacheServiceClient = v1beta.CacheServiceClient;
exports.CacheServiceClient = CacheServiceClient;
const DiscussServiceClient = v1beta.DiscussServiceClient;
exports.DiscussServiceClient = DiscussServiceClient;
const FileServiceClient = v1beta.FileServiceClient;
exports.FileServiceClient = FileServiceClient;
const GenerativeServiceClient = v1beta.GenerativeServiceClient;
exports.GenerativeServiceClient = GenerativeServiceClient;
const ModelServiceClient = v1beta.ModelServiceClient;
exports.ModelServiceClient = ModelServiceClient;
const PermissionServiceClient = v1beta.PermissionServiceClient;
exports.PermissionServiceClient = PermissionServiceClient;
const PredictionServiceClient = v1beta.PredictionServiceClient;
exports.PredictionServiceClient = PredictionServiceClient;
const RetrieverServiceClient = v1beta.RetrieverServiceClient;
exports.RetrieverServiceClient = RetrieverServiceClient;
const TextServiceClient = v1beta.TextServiceClient;
exports.TextServiceClient = TextServiceClient;
exports.default = {
    v1,
    v1alpha,
    v1beta,
    v1beta2,
    v1beta3,
    CacheServiceClient,
    DiscussServiceClient,
    FileServiceClient,
    GenerativeServiceClient,
    ModelServiceClient,
    PermissionServiceClient,
    PredictionServiceClient,
    RetrieverServiceClient,
    TextServiceClient,
};
const protos = require("../protos/protos");
exports.protos = protos;
//# sourceMappingURL=index.js.map