(()=>{var e={};e.id=7177,e.ids=[7177],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>i});var s=r(34386),o=r(44999);async function i(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17528:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>c});var o=r(96559),i=r(48088),a=r(37719),n=r(32190),u=r(2507);async function c(e){let t=await (0,u.x)();try{let{name:r}=await e.json();if(!r||"string"!=typeof r||0===r.trim().length)return n.NextResponse.json({error:"Configuration name is required and must be a non-empty string"},{status:400});if(r.length>255)return n.NextResponse.json({error:"Configuration name must be 255 characters or less"},{status:400});let{data:s,error:o}=await t.from("custom_api_configs").insert([{name:r,user_id:"00000000-0000-0000-0000-000000000000"}]).select().single();if(o)return n.NextResponse.json({error:"Failed to create custom API configuration",details:o.message},{status:500});return n.NextResponse.json(s,{status:201})}catch(e){if("SyntaxError"===e.name)return n.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function p(e){let t=await (0,u.x)();try{let{data:r,error:s}=await t.from("custom_api_configs").select("id, name, created_at, updated_at, routing_strategy").order("created_at",{ascending:!1}).limit(100);if(s)return n.NextResponse.json({error:"Failed to fetch custom API configurations",details:s.message},{status:500});let o=n.NextResponse.json(r||[],{status:200}),i="true"===e.headers.get("X-Prefetch");return o.headers.set("Cache-Control",`private, max-age=${i?600:120}, stale-while-revalidate=300`),o.headers.set("X-Content-Type-Options","nosniff"),o.headers.set("Vary","X-Prefetch"),o}catch(e){return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/custom-configs/route",pathname:"/api/custom-configs",filename:"route",bundlePath:"app/api/custom-configs/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\custom-configs\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:m}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(17528));module.exports=s})();