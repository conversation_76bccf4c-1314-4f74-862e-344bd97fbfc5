import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    if (!supabase) {
      console.error('Supabase client could not be initialized in /api/activity.');
      return NextResponse.json({ error: 'Server configuration error.' }, { status: 500 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');

    // Get recent request logs for activity feed
    const { data: logs, error: logsError } = await supabase
      .from('request_logs')
      .select(`
        id,
        request_timestamp,
        status_code,
        llm_model_name,
        llm_provider_name,
        error_message,
        cost,
        input_tokens,
        output_tokens,
        custom_api_config_id
      `)
      .order('request_timestamp', { ascending: false })
      .limit(limit);

    if (logsError) {
      console.error('Error fetching activity logs:', logsError);
      return NextResponse.json({ error: 'Failed to fetch activity data' }, { status: 500 });
    }

    // Get recent API key additions
    const { data: apiKeys, error: keysError } = await supabase
      .from('api_keys')
      .select(`
        id,
        created_at,
        label,
        provider,
        predefined_model_id
      `)
      .order('created_at', { ascending: false })
      .limit(5);

    if (keysError) {
      console.error('Error fetching API keys:', keysError);
    }

    // Get recent custom config updates
    const { data: configs, error: configsError } = await supabase
      .from('custom_api_configs')
      .select(`
        id,
        updated_at,
        name,
        routing_strategy
      `)
      .order('updated_at', { ascending: false })
      .limit(5);

    if (configsError) {
      console.error('Error fetching configs:', configsError);
    }

    // Combine and format activity data
    const activities: any[] = [];

    // Add request activities
    if (logs) {
      logs.forEach((log: any) => {
        const isSuccess = log.status_code >= 200 && log.status_code < 300;
        const isError = log.status_code >= 400;
        
        let action = 'Request completed';
        let status = 'success';
        
        if (isError) {
          action = 'Request failed';
          status = 'error';
        } else if (log.status_code >= 300) {
          action = 'Request redirected';
          status = 'warning';
        }

        activities.push({
          id: `request-${log.id}`,
          type: 'request',
          action,
          model: log.llm_model_name || 'Unknown Model',
          provider: log.llm_provider_name,
          timestamp: log.request_timestamp,
          status,
          details: log.error_message,
          cost: log.cost,
          tokens: log.input_tokens && log.output_tokens ? 
            `${log.input_tokens} in, ${log.output_tokens} out` : null
        });
      });
    }

    // Add API key activities
    if (apiKeys) {
      apiKeys.forEach((key: any) => {
        activities.push({
          id: `key-${key.id}`,
          type: 'api_key',
          action: 'New API key added',
          model: key.predefined_model_id || key.provider,
          provider: key.provider,
          timestamp: key.created_at,
          status: 'info',
          details: key.label
        });
      });
    }

    // Add config activities
    if (configs) {
      configs.forEach((config: any) => {
        activities.push({
          id: `config-${config.id}`,
          type: 'config',
          action: 'Configuration updated',
          model: config.name,
          provider: config.routing_strategy,
          timestamp: config.updated_at,
          status: 'info',
          details: `Routing strategy: ${config.routing_strategy}`
        });
      });
    }

    // Sort by timestamp and limit
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    const limitedActivities = activities.slice(0, limit);

    // Format timestamps
    const formattedActivities = limitedActivities.map(activity => ({
      ...activity,
      time: getTimeAgo(new Date(activity.timestamp))
    }));

    return NextResponse.json({ 
      activities: formattedActivities,
      total: activities.length 
    });

  } catch (error: Error) {
    console.error('Error in /api/activity:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}

function getTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  return `${Math.floor(diffInSeconds / 86400)} days ago`;
}
