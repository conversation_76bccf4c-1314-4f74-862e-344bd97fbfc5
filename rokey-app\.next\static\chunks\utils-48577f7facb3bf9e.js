"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5738],{5777:(e,t,a)=>{a.d(t,{zf:()=>o});class r{set(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{ttl:r=3e5,tags:i=[],priority:n="medium",serialize:s=!1}=a,l={data:s?JSON.parse(JSON.stringify(t)):t,timestamp:Date.now(),ttl:r,accessCount:0,lastAccessed:Date.now(),tags:i,priority:n};this.cache.size>=this.maxSize&&this.evictLeastUsed(),this.cache.set(e,l)}get(e){let t=this.cache.get(e);return t?this.isExpired(t)?(this.cache.delete(e),null):(t.accessCount++,t.lastAccessed=Date.now(),t.data):null}getStale(e){let t=this.cache.get(e);if(!t)return{data:null,isStale:!1};let a=this.isExpired(t);return t.accessCount++,t.lastAccessed=Date.now(),{data:t.data,isStale:a}}has(e){let t=this.cache.get(e);return!!t&&(!this.isExpired(t)||(this.cache.delete(e),!1))}delete(e){return this.cache.delete(e)}invalidateByTags(e){let t=0;for(let[a,r]of this.cache.entries())r.tags.some(t=>e.includes(t))&&(this.cache.delete(a),t++);return t}clear(){this.cache.clear()}getStats(){let e=Array.from(this.cache.values()),t=Date.now();return{size:this.cache.size,maxSize:this.maxSize,hitRate:this.calculateHitRate(),averageAge:e.reduce((e,a)=>e+(t-a.timestamp),0)/e.length||0,totalAccesses:e.reduce((e,t)=>e+t.accessCount,0),expiredEntries:e.filter(e=>this.isExpired(e)).length,priorityDistribution:{high:e.filter(e=>"high"===e.priority).length,medium:e.filter(e=>"medium"===e.priority).length,low:e.filter(e=>"low"===e.priority).length}}}async preload(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=this.get(e);if(r)return this.backgroundRefresh(e,t,a),r;let i=await t();return this.set(e,i,a),i}async backgroundRefresh(e,t,a){try{let r=await t();this.set(e,r,a)}catch(e){}}isExpired(e){return Date.now()-e.timestamp>e.ttl}evictLeastUsed(){if(0===this.cache.size)return;let[e]=Array.from(this.cache.entries()).sort((e,t)=>{let[,a]=e,[,r]=t,i={low:0,medium:1,high:2},n=i[a.priority]-i[r.priority];return 0!==n?n:a.lastAccessed-r.lastAccessed})[0];this.cache.delete(e)}calculateHitRate(){let e=Array.from(this.cache.values()).reduce((e,t)=>e+t.accessCount,0);return e>0?this.cache.size/e*100:0}startCleanup(){this.cleanupInterval=setInterval(()=>{this.cleanup()},6e4)}cleanup(){Date.now();let e=[];for(let[t,a]of this.cache.entries())this.isExpired(a)&&e.push(t);e.forEach(e=>this.cache.delete(e)),e.length}destroy(){this.cleanupInterval&&clearInterval(this.cleanupInterval),this.cache.clear()}constructor(e=100){this.cache=new Map,this.cleanupInterval=null,this.maxSize=e,this.startCleanup()}}let i=new r(200),n={static:{ttl:864e5,priority:"high",tags:["static"]},user:{ttl:12e4,priority:"high",tags:["user"]},system:{ttl:3e4,priority:"low",tags:["system"]},pricing:{ttl:36e5,priority:"medium",tags:["pricing"]}},s={LANDING_FEATURES:"landing:features",PRICING_TIERS:"pricing:tiers",PRICING_COMPARISON:"pricing:comparison",SYSTEM_STATUS:"system:status",SYSTEM_MODELS:"system:models",USER_CONFIGS:"user:configs",USER_ANALYTICS:"user:analytics"};class l{static getInstance(){return l.instance||(l.instance=new l),l.instance}trackNavigation(e,t){let a="".concat(e,"->").concat(t),r=this.userBehavior.get(a)||0;this.userBehavior.set(a,r+1),r>2&&this.schedulePrefetch(t)}schedulePrefetch(e){this.prefetchQueue.has(e)||(this.prefetchQueue.add(e),this.processPrefetchQueue())}async processPrefetchQueue(){if(!this.isProcessing&&0!==this.prefetchQueue.size){for(let e of(this.isProcessing=!0,this.prefetchQueue)){try{await this.prefetchRoute(e),this.prefetchQueue.delete(e)}catch(e){}await new Promise(e=>setTimeout(e,100))}this.isProcessing=!1}}async prefetchRoute(e){let t={"/dashboard":()=>this.prefetchDashboardData(),"/pricing":()=>this.prefetchPricingData(),"/auth/signup":()=>this.prefetchAuthData(),"/features":()=>this.prefetchFeaturesData()}[e];t&&await t()}async prefetchDashboardData(){let e=[this.cacheIfNotExists(s.USER_CONFIGS,"/api/custom-configs",n.user),this.cacheIfNotExists(s.USER_ANALYTICS,"/api/analytics",n.user),this.cacheIfNotExists(s.SYSTEM_STATUS,"/api/system-status",n.system)];await Promise.allSettled(e)}async prefetchPricingData(){let e=[this.cacheIfNotExists(s.PRICING_TIERS,"/api/pricing/tiers",n.pricing),this.cacheIfNotExists(s.PRICING_COMPARISON,"/api/pricing/comparison",n.pricing)];await Promise.allSettled(e)}async prefetchAuthData(){let e=[this.cacheIfNotExists(s.PRICING_TIERS,"/api/pricing/tiers",n.pricing)];await Promise.allSettled(e)}async prefetchFeaturesData(){let e=[this.cacheIfNotExists(s.LANDING_FEATURES,"/api/features",n.static),this.cacheIfNotExists(s.SYSTEM_MODELS,"/api/models",n.static)];await Promise.allSettled(e)}async cacheIfNotExists(e,t,a){let r=i.get(e);if(r)return r;try{let r=await fetch(t);if(r.ok){let t=await r.json();return i.set(e,t,a),t}}catch(e){}}constructor(){this.prefetchQueue=new Set,this.isProcessing=!1,this.userBehavior=new Map}}let o=l.getInstance()},13744:(e,t,a)=>{a.d(t,{Lz:()=>n});var r=a(49509);let i=!!("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function n(e){"serviceWorker"in navigator&&new URL(r.env.PUBLIC_URL||"",window.location.href).origin===window.location.origin&&window.addEventListener("load",()=>{var t,a;let r="/sw.js";i?(t=r,a=e,fetch(t,{headers:{"Service-Worker":"script"}}).then(e=>{let r=e.headers.get("content-type");404===e.status||null!=r&&-1===r.indexOf("javascript")?navigator.serviceWorker.ready.then(e=>{e.unregister().then(()=>{window.location.reload()})}):s(t,a)}).catch(()=>{}),navigator.serviceWorker.ready.then(()=>{})):s(r,e)})}function s(e,t){navigator.serviceWorker.register(e).then(e=>{e.onupdatefound=()=>{let a=e.installing;null!=a&&(a.onstatechange=()=>{"installed"===a.state&&(navigator.serviceWorker.controller?t&&t.onUpdate&&t.onUpdate(e):t&&t.onSuccess&&t.onSuccess(e))})}}).catch(e=>{t&&t.onError&&t.onError(e)})}},24403:(e,t,a)=>{a.d(t,{l2:()=>c,mx:()=>o});var r=a(12115);let i=new Map,n={hits:0,misses:0},s=new Set,l=!1,o=e=>{let{configId:t,enablePrefetch:a=!0,cacheTimeout:o=3e5,staleTimeout:c=3e4}=e,[u,d]=(0,r.useState)([]),[m,h]=(0,r.useState)(!1),[g,p]=(0,r.useState)(!1),[f,y]=(0,r.useState)(null),v=(0,r.useRef)(null),w=(0,r.useRef)(null),b=(0,r.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];arguments.length>2&&void 0!==arguments[2]&&arguments[2];let r=i.get(e),l=Date.now();if(!t&&r&&l-r.timestamp<o)return n.hits++,l-r.timestamp>c&&!r.isStale&&(r.isStale=!0,a&&(s.add(e),k())),r.data;n.misses++,v.current&&v.current.abort(),v.current=new AbortController;try{let t="/api/chat/conversations?custom_api_config_id=".concat(e),a=await fetch(t,{signal:v.current.signal,headers:{"Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"}});if(!a.ok)throw Error("Failed to fetch chat history: ".concat(a.status," ").concat(a.statusText));let r=await a.json();return i.set(e,{data:r,timestamp:l,isStale:!1}),r}catch(e){if("AbortError"===e.name)throw e;if(r&&r.data.length>0)return r.data;throw e}},[o,c,a]),k=(0,r.useCallback)(async()=>{if(!l&&0!==s.size){l=!0;try{let e=Array.from(s);for(let t of(s.clear(),e))try{await b(t,!0,!0),await new Promise(e=>setTimeout(e,100))}catch(e){}}finally{l=!1}}},[b]),C=(0,r.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!t)return;let a=i.get(t);!e&&a&&a.data.length>0&&(d(a.data),p(a.isStale),y(null)),h(!0),w.current=t;try{let a=await b(t,e);w.current===t&&(d(a),p(!1),y(null))}catch(e){"AbortError"!==e.name&&w.current===t&&y("Failed to load chat history: ".concat(e.message))}finally{w.current===t&&h(!1)}},[t,b]),S=(0,r.useCallback)(async e=>{a&&(s.add(e),k())},[a,k]),T=(0,r.useCallback)(e=>{e?i.delete(e):i.clear()},[]),_=(0,r.useCallback)(()=>({size:i.size,hits:n.hits,misses:n.misses}),[]);return(0,r.useEffect)(()=>{t?C():(d([]),h(!1),y(null),p(!1))},[t,C]),(0,r.useEffect)(()=>()=>{v.current&&v.current.abort()},[]),{chatHistory:u,isLoading:m,isStale:g,error:f,refetch:C,prefetch:S,invalidateCache:T,getCacheStats:_}},c=()=>{let e=(0,r.useRef)(new Set);return{prefetchChatHistory:(0,r.useCallback)(async t=>{e.current.has(t)||(e.current.add(t),s.add(t),setTimeout(()=>{s.size>0&&(async()=>{if(!l){l=!0;try{let e=Array.from(s);for(let t of(s.clear(),e)){try{let e="/api/chat/conversations?custom_api_config_id=".concat(t),a=await fetch(e,{headers:{"X-Prefetch":"true"}});if(a.ok){let e=await a.json();i.set(t,{data:e,timestamp:Date.now(),isStale:!1})}}catch(e){}await new Promise(e=>setTimeout(e,100))}}finally{l=!1}}})()},200))},[])}}},28003:(e,t,a)=>{a.d(t,{_:()=>n});var r=a(12115);let i={};function n(){let[e,t]=(0,r.useState)({}),a=(0,r.useRef)({}),n=(0,r.useCallback)(e=>{let t=i[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),s=(0,r.useCallback)(e=>{let t=i[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete i[e],null):t.data},[]),l=(0,r.useCallback)(async function(e){var r;let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"medium";if(n(e))return s(e);if(null==(r=i[e])?void 0:r.isLoading)return null;a.current[e]&&a.current[e].abort();let o=new AbortController;a.current[e]=o,i[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===l?await new Promise(e=>setTimeout(e,200)):"medium"===l&&await new Promise(e=>setTimeout(e,50));let[a,r,n,s,c]=await Promise.allSettled([fetch("/api/custom-configs",{signal:o.signal}),fetch("/api/keys?custom_config_id=".concat(e),{signal:o.signal}),fetch("/api/user/custom-roles",{signal:o.signal}),fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({}),signal:o.signal}),fetch("/api/custom-configs/".concat(e,"/default-chat-key"),{signal:o.signal})]),u=null,d=[],m=[],h=[],g=null;if("fulfilled"===a.status&&a.value.ok&&(u=(await a.value.json()).find(t=>t.id===e)),"fulfilled"===r.status&&r.value.ok&&(d=await r.value.json()),"fulfilled"===n.status&&n.value.ok&&(m=await n.value.json()),"fulfilled"===s.status&&s.value.ok&&(h=(await s.value.json()).models||[]),"fulfilled"===c.status&&c.value.ok){let e=await c.value.json();g=(null==e?void 0:e.id)||null}let p={configDetails:u,apiKeys:d,userCustomRoles:m,models:h,defaultChatKeyId:g};return i[e]={data:p,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),p}catch(a){if("AbortError"===a.name)return null;return delete i[e],t(t=>({...t,[e]:"error"})),null}finally{delete a.current[e]}},[n,s]),o=(0,r.useCallback)(e=>({onMouseEnter:()=>{n(e)||l(e,"high")}}),[l,n]),c=(0,r.useCallback)(e=>{delete i[e],t(t=>{let a={...t};return delete a[e],a})},[]),u=(0,r.useCallback)(()=>{Object.keys(i).forEach(e=>{delete i[e]}),t({})},[]);return{prefetchManageKeysData:l,getCachedData:s,isCached:n,createHoverPrefetch:o,clearCache:c,clearAllCache:u,getStatus:(0,r.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,r.useCallback)(()=>({cachedConfigs:Object.keys(i),cacheSize:Object.keys(i).length,totalCacheAge:Object.values(i).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(i).length}),[]),prefetchStatus:e}}},34962:(e,t,a)=>{a.d(t,{rT:()=>l});var r=a(35695),i=a(12115);let n={"/dashboard":{title:"Dashboard",subtitle:"Overview & analytics"},"/playground":{title:"Playground",subtitle:"Test your models",parent:"/dashboard"},"/my-models":{title:"My Models",subtitle:"API key management",parent:"/dashboard"},"/routing-setup":{title:"Routing Setup",subtitle:"Configure routing",parent:"/dashboard"},"/logs":{title:"Logs",subtitle:"Request history",parent:"/dashboard"},"/training":{title:"Prompt Engineering",subtitle:"Custom prompts",parent:"/dashboard"},"/analytics":{title:"Analytics",subtitle:"Advanced insights",parent:"/dashboard"},"/add-keys":{title:"Add Keys",subtitle:"API key setup",parent:"/my-models"}},s=[{pattern:/^\/my-models\/([^\/]+)$/,getConfig:e=>({title:"Manage Keys",subtitle:"API key management",parent:"/my-models"})},{pattern:/^\/routing-setup\/([^\/]+)$/,getConfig:e=>({title:"Routing Configuration",subtitle:"Advanced routing setup",parent:"/routing-setup"})},{pattern:/^\/routing-setup\/([^\/]+)$/,getConfig:e=>({title:"Routing Setup",subtitle:"Advanced configuration",parent:"/routing-setup"})},{pattern:/^\/playground\?config=([^&]+)/,getConfig:(e,t)=>({title:"Playground",subtitle:"Testing configuration",parent:"/playground"})}];function l(){let e=(0,r.usePathname)(),t=(0,r.useSearchParams)(),a=(0,i.useMemo)(()=>{let a=n[e];if(a)return{title:a.title,subtitle:a.subtitle,parent:a.parent};for(let a of s){let r=e.match(a.pattern);if(r)return a.getConfig(r,t)}let r=e+(t.toString()?"?".concat(t.toString()):"");for(let e of s){let a=r.match(e.pattern);if(a)return e.getConfig(a,t)}return{title:"Dashboard",subtitle:"Overview",parent:void 0}},[e,t]),l=(0,i.useMemo)(()=>{let t=[];if(a.parent&&n[a.parent]){let e=n[a.parent];t.push({title:e.title,subtitle:e.subtitle,href:a.parent,isActive:!1})}return t.push({title:a.title,subtitle:a.subtitle,href:e,isActive:!0}),t},[a,e]);return{breadcrumb:(0,i.useMemo)(()=>({title:a.title,subtitle:a.subtitle}),[a]),breadcrumbTrail:l,pageTitle:(0,i.useMemo)(()=>"".concat(a.title," - RoKey"),[a]),currentPage:{title:a.title,subtitle:a.subtitle,path:e}}}},37843:(e,t,a)=>{a.d(t,{C:()=>l,e:()=>o});var r=a(35695),i=a(12115);class n{setRouter(e){this.router=e}async prefetchRoute(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.router)return;let{priority:a="low",delay:r=0,condition:i}=t;if(i&&!i())return;let n=this.prefetchedRoutes.get(e);n&&n.prefetched&&Date.now()-n.timestamp<3e5||(this.prefetchQueue.push({route:e,options:t}),this.prefetchedRoutes.set(e,{route:e,timestamp:Date.now(),prefetched:!1}),this.processQueue())}async processQueue(){if(!this.isProcessing&&0!==this.prefetchQueue.length){for(this.isProcessing=!0,this.prefetchQueue.sort((e,t)=>{let a={high:0,low:1};return a[e.options.priority||"low"]-a[t.options.priority||"low"]});this.prefetchQueue.length>0;){let{route:e,options:t}=this.prefetchQueue.shift();try{if(t.delay&&t.delay>0&&await new Promise(e=>setTimeout(e,t.delay)),t.condition&&!t.condition())continue;await this.router.prefetch(e),await this.prefetchBundles(e);let a=this.prefetchedRoutes.get(e);a&&(a.prefetched=!0,a.timestamp=Date.now()),await new Promise(e=>setTimeout(e,50))}catch(e){}}this.isProcessing=!1}}async prefetchBundles(e){try{["https://fonts.googleapis.com","https://fonts.gstatic.com"].forEach(e=>{if(document.querySelector('link[href="'.concat(e,'"][rel="preconnect"]')))return;let t=document.createElement("link");t.rel="preconnect",t.href=e,t.crossOrigin="anonymous",document.head.appendChild(t)})}catch(e){}}cleanup(){let e=Date.now();for(let[t,a]of this.prefetchedRoutes.entries())e-a.timestamp>6e5&&this.prefetchedRoutes.delete(t)}constructor(){this.prefetchedRoutes=new Map,this.router=null,this.prefetchQueue=[],this.isProcessing=!1}}let s=new n,l=()=>{let e=(0,r.useRouter)(),t=(0,i.useRef)();(0,i.useEffect)(()=>(s.setRouter(e),t.current=setInterval(()=>{s.cleanup()},3e5),()=>{t.current&&clearInterval(t.current)}),[e]);let a=(0,i.useCallback)((e,t)=>{s.prefetchRoute(e,t)},[]),n=(0,i.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return{onMouseEnter:()=>{a(e,{priority:"high",delay:t})}}},[a]),l=(0,i.useCallback)((e,t)=>{if(!t)return;let r=new IntersectionObserver(t=>{t.forEach(t=>{t.isIntersecting&&(a(e,{priority:"low",delay:200}),r.disconnect())})},{threshold:.1});return r.observe(t),()=>r.disconnect()},[a]);return{prefetchRoute:a,prefetchOnHover:n,prefetchOnVisible:l}},o=()=>{let{prefetchRoute:e}=l(),t=(0,i.useRef)({lastActivity:Date.now(),isIdle:!1,mouseMovements:0});return(0,i.useEffect)(()=>{let e,a,r=()=>{t.current.lastActivity=Date.now(),t.current.isIdle=!1,clearTimeout(e),e=setTimeout(()=>{t.current.isIdle=!0},3e3)},i=()=>{t.current.mouseMovements++,r()},n=()=>{r()};return document.addEventListener("mousemove",i),document.addEventListener("keypress",n),document.addEventListener("click",r),document.addEventListener("scroll",r),a=setInterval(()=>{t.current.mouseMovements=0},1e4),()=>{document.removeEventListener("mousemove",i),document.removeEventListener("keypress",n),document.removeEventListener("click",r),document.removeEventListener("scroll",r),clearTimeout(e),clearInterval(a)}},[]),{prefetchWhenIdle:(0,i.useCallback)(a=>{let r=setInterval(()=>{t.current.isIdle&&t.current.mouseMovements<5&&a.forEach((a,r)=>{e(a,{priority:"low",delay:500*r,condition:()=>t.current.isIdle})})},2e3);return()=>clearInterval(r)},[e]),isUserIdle:()=>t.current.isIdle}}},38456:(e,t,a)=>{a.d(t,{Il:()=>d,Rf:()=>c,aU:()=>u,gI:()=>i});let r=e=>![/default_key/i,/attempt_\d+/i,/status_\d+/i,/failed/i,/success/i,/complexity_rr/i,/fallback_position/i,/^[a-f0-9-]{8,}/i,/_then_/i,/classification_/i,/no_prompt/i,/error/i].some(t=>t.test(e))&&/^[a-z_]+$/i.test(e)&&e.length>2&&e.length<50,i=e=>{if(!e)return{text:"N/A",type:"fallback"};if(r(e))return{text:o(e),type:"role",details:"Role-based routing: ".concat(o(e))};if(e.includes("default_key")&&e.includes("success")){let t=e.match(/attempt_(\d+)/),a=t?parseInt(t[1]):1;return{text:1===a?"Default Key":"Default Key (Attempt ".concat(a,")"),type:"success",details:a>1?"Required ".concat(a," attempts to succeed"):void 0}}if(e.includes("default_key")&&e.includes("failed")){let t=e.match(/attempt_(\d+)/),a=e.match(/status_(\w+)/),r=t?parseInt(t[1]):1,i=a?a[1]:"unknown";return{text:"Failed (Attempt ".concat(r,")"),type:"error",details:"Failed with status: ".concat(i)}}if(e.includes("default_all")&&e.includes("attempts_failed")){let t=e.match(/default_all_(\d+)_attempts/),a=t?parseInt(t[1]):0;return{text:"All Keys Failed (".concat(a," attempts)"),type:"error",details:"Tried ".concat(a," different API keys, all failed")}}if(e.includes("complexity_rr_clsf_")||e.includes("complexity_level_")){let t=e.match(/complexity_rr_clsf_(\d+)_used_lvl_(\d+)/);if(t){let e=t[1],a=t[2];return e===a?{text:"Complexity Level ".concat(a),type:"success",details:"Classified and routed to complexity level ".concat(a)}:{text:"Complexity ".concat(e,"→").concat(a),type:"success",details:"Classified as level ".concat(e,", routed to available level ").concat(a)}}let a=e.match(/complexity_level_(\d+)/);if(a){let e=a[1];return{text:"Complexity Level ".concat(e),type:"success",details:"Routed based on prompt complexity analysis"}}}if(e.includes("fallback_position_")){let t=e.match(/fallback_position_(\d+)/),a=t?parseInt(t[1]):0;return{text:"Fallback Key #".concat(a+1),type:"success",details:"Used fallback key at position ".concat(a+1)}}if(e.includes("intelligent_role_")){let t=e.match(/intelligent_role_(.+)$/),a=t?t[1]:"unknown";return{text:"Smart: ".concat(o(a)),type:"role",details:"AI detected role: ".concat(o(a))}}return n(e)},n=e=>{let t=e.match(/complexity[_\s]*(\d+)/i);if(t){let e=t[1];return{text:"Complexity Level ".concat(e),type:"success",details:"Extracted complexity level ".concat(e," from routing pattern")}}let a=s(e);if(a)return{text:o(a),type:"role",details:"Extracted role: ".concat(o(a))};let r=e.match(/fallback[_\s]*(\d+)/i);if(r){let e=parseInt(r[1]);return{text:"Fallback Key #".concat(e+1),type:"success",details:"Extracted fallback position ".concat(e+1)}}let i=e.match(/attempt[_\s]*(\d+)/i);if(i){let t=parseInt(i[1]),a=e.toLowerCase().includes("success"),r=e.toLowerCase().includes("fail");if(a)return{text:1===t?"Default Key":"Default Key (Attempt ".concat(t,")"),type:"success",details:"Extracted success on attempt ".concat(t)};if(r)return{text:"Failed (Attempt ".concat(t,")"),type:"error",details:"Extracted failure on attempt ".concat(t)}}return{text:l(e),type:"fallback",details:"Raw routing pattern: ".concat(e)}},s=e=>{let t=e.match(/classified_as_([a-z_]+)_/i);if(t&&r(t[1]))return t[1];let a=e.match(/role_([a-z_]+)_/i);if(a&&r(a[1]))return a[1];let i=e.match(/fb_role_([a-z_]+)/i);return i&&r(i[1])?i[1]:null},l=e=>{let t=e.replace(/^default_key_[a-f0-9-]+_/i,"").replace(/_attempt_\d+$/i,"").replace(/_status_\w+$/i,"").replace(/_key_selected$/i,"").replace(/_then_.*$/i,"").replace(/^complexity_rr_/i,"").replace(/_no_.*$/i,"");return t&&t.length>2&&t.length<30&&r(t)?o(t):e.replace(/_/g," ").replace(/([a-z])([A-Z])/g,"$1 $2").split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ").substring(0,30)+(e.length>30?"...":"")},o=e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),c=e=>{switch(e){case"role":return"px-2 py-1 bg-blue-100 text-blue-800 rounded-lg text-xs font-medium";case"success":return"px-2 py-1 bg-green-100 text-green-800 rounded-lg text-xs font-medium";case"error":return"px-2 py-1 bg-red-100 text-red-800 rounded-lg text-xs font-medium";default:return"px-2 py-1 bg-orange-100 text-orange-800 rounded-lg text-xs font-medium"}},u=e=>e?({openai:"OpenAI",anthropic:"Anthropic",google:"Google",openrouter:"OpenRouter",deepseek:"DeepSeek",xai:"xAI"})[e.toLowerCase()]||e:"N/A",d=e=>e?e.replace(/^(gpt-|claude-|gemini-|meta-llama\/|deepseek-|grok-)/,"").replace(/-/g," ").replace(/\b\w/g,e=>e.toUpperCase()):"N/A"},41e3:(e,t,a)=>{a.d(t,{n4:()=>s,w6:()=>n});var r=a(12115);let i={initializing:50,analyzing:150,routing:200,complexity_analysis:250,role_classification:300,preparing:150,connecting:200,generating:400,typing:0,finalizing:100,complete:0};function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{enableAutoProgression:t=!0,stageDurations:a={},onStageChange:n}=e,[s,l]=(0,r.useState)("initializing"),[o,c]=(0,r.useState)(!1),[u,d]=(0,r.useState)([]),m=(0,r.useRef)(0),h=(0,r.useRef)([]);({...i,...a});let g=(0,r.useCallback)(()=>{h.current.forEach(e=>clearTimeout(e)),h.current=[]},[]),p=(0,r.useCallback)(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],a=Date.now();if("connecting"===e){l(e),d(t=>[...t,{stage:e,timestamp:a}]),null==n||n(e,a);let t=setTimeout(()=>{l("routing"),d(e=>[...e,{stage:"routing",timestamp:Date.now()}]),null==n||n("routing",Date.now())},2e3);h.current.push(t);return}t&&g(),l(e),d(t=>[...t,{stage:e,timestamp:a}]),null==n||n(e,a)},[n,g]),f=(0,r.useCallback)(()=>{let e;c(!0),m.current=Date.now(),d([{stage:"initializing",timestamp:Date.now()}]),l("initializing");let t=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30,a=(Math.random()-.5)*2*(t/100*e);return Math.max(200,Math.round(e+a))},a=setTimeout(()=>{p("analyzing",!1)},e=0+t(900,35)),r=setTimeout(()=>{p("complexity_analysis",!1)},e+=t(1200,40)),i=setTimeout(()=>{p("role_classification",!1)},e+=t(1500,35)),n=setTimeout(()=>{p("preparing",!1)},e+=t(1e3,40)),s=setTimeout(()=>{p("connecting",!1)},e+=t(1200,35)),o=setTimeout(()=>{p("routing",!1)},e+=t(1500,40)),u=setTimeout(()=>{p("generating",!1)},e+=t(1200,35));h.current.push(a,r,i,n,s,o,u)},[n,p]),y=(0,r.useCallback)(()=>{g();let e=Date.now();l("typing"),d(t=>[...t,{stage:"typing",timestamp:e}]),null==n||n("typing",e)},[g,n]),v=(0,r.useCallback)(()=>{g(),p("complete"),c(!1)},[g,p]),w=(0,r.useCallback)(()=>{g();let e=Date.now();l("generating"),d(t=>[...t,{stage:"generating",timestamp:e}]),null==n||n("generating",e)},[g,n]),b=(0,r.useCallback)(e=>{},[]),k=(0,r.useCallback)(()=>{g(),l("initializing"),c(!1),d([]),m.current=0},[g]),C=(0,r.useCallback)(()=>0===m.current?0:Date.now()-m.current,[]);return(0,r.useEffect)(()=>()=>{g()},[g]),{currentStage:s,isActive:o,stageHistory:u,startProcessing:f,updateStage:p,markStreaming:y,markComplete:v,markOrchestrationStarted:w,updateOrchestrationStatus:b,reset:k,getProcessingDuration:C}}(e),[a,n]=(0,r.useState)(new Set),s=(0,r.useCallback)(e=>{e.get("x-rokey-role-used"),e.get("x-rokey-routing-strategy"),e.get("x-rokey-complexity-level"),e.get("x-rokey-api-key-provider"),t.updateStage("generating")},[t]),l=(0,r.useCallback)(e=>{e.includes("[Complexity Classification]")&&!a.has("complexity")&&(t.updateStage("complexity_analysis"),n(e=>new Set([...e,"complexity"]))),e.includes("[Intelligent Role Strategy]")&&!a.has("role")&&(t.updateStage("role_classification"),n(e=>new Set([...e,"role"]))),e.includes("FIRST TOKEN:")&&!a.has("streaming")&&(t.markStreaming(),n(e=>new Set([...e,"streaming"])))},[t,a]),o=(0,r.useCallback)(()=>{t.reset(),n(new Set)},[t]);return{...t,analyzeResponseHeaders:s,analyzeStreamChunk:l,reset:o,detectedStages:Array.from(a)}}function s(e){if(!(e.length<2)){for(let t=1;t<e.length;t++){let a=e[t],r=e[t-1];a.timestamp,r.timestamp}e[e.length-1].timestamp,e[0].timestamp}}},42126:(e,t,a)=>{a.d(t,{v:()=>o});var r=a(12115),i=a(35695),n=a(42724),s=a(37843);let l={maxConcurrent:3,idleTimeout:2e3,hoverDelay:100,backgroundDelay:5e3};function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,i.usePathname)();(0,i.useRouter)();let{predictions:a,isLearning:o}=(0,n.x)(),{prefetchRoute:c}=(0,s.C)(),u={...l,...e},d=(0,r.useRef)([]),m=(0,r.useRef)(new Set),h=(0,r.useRef)(null),g=(0,r.useCallback)(()=>{let e={immediate:[],onIdle:[],onHover:[],background:[]};switch(t){case"/dashboard":e.immediate=["/playground"],e.onIdle=["/my-models","/logs"],e.background=["/routing-setup","/analytics"];break;case"/my-models":e.immediate=["/playground","/routing-setup"],e.onIdle=["/logs"],e.background=["/dashboard","/analytics"];break;case"/playground":e.immediate=["/logs"],e.onIdle=["/my-models"],e.background=["/dashboard","/training"];break;case"/logs":e.immediate=["/playground"],e.onIdle=["/analytics"],e.background=["/my-models","/dashboard"];break;case"/routing-setup":e.immediate=["/playground"],e.onIdle=["/my-models"],e.background=["/logs","/dashboard"];break;default:e.onIdle=["/dashboard","/playground"]}return o&&a.length>0&&(a.slice(0,2).forEach(t=>{e.immediate.includes(t)||e.immediate.unshift(t)}),a.slice(2).forEach(t=>{e.onIdle.includes(t)||e.onIdle.push(t)})),Object.keys(e).forEach(a=>{e[a]=e[a].filter(e=>e!==t)}),e},[t,a,o]),p=(0,r.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"medium";if(m.current.has(e)||m.current.size>=u.maxConcurrent)return void d.current.push(e);m.current.add(e);try{await c(e,{priority:"medium"===t?"low":t,delay:"high"===t?0:"medium"===t?100:300})}catch(e){}finally{if(m.current.delete(e),d.current.length>0){let e=d.current.shift();e&&setTimeout(()=>p(e,"low"),100)}}},[c,u.maxConcurrent]);(0,r.useEffect)(()=>{g().immediate.forEach((e,t)=>{setTimeout(()=>{p(e,"high")},50*t)})},[t,g,p]),(0,r.useEffect)(()=>{let e=g();return h.current&&cancelIdleCallback(h.current),h.current=requestIdleCallback(()=>{e.onIdle.forEach((e,t)=>{setTimeout(()=>{p(e,"medium")},200*t)})},{timeout:u.idleTimeout}),()=>{h.current&&cancelIdleCallback(h.current)}},[t,g,p,u.idleTimeout]),(0,r.useEffect)(()=>{let e=g(),t=setTimeout(()=>{e.background.forEach((e,t)=>{setTimeout(()=>{p(e,"low")},500*t)})},u.backgroundDelay);return()=>clearTimeout(t)},[t,g,p,u.backgroundDelay]);let f=(0,r.useCallback)(e=>({onMouseEnter:()=>{setTimeout(()=>{p(e,"high")},u.hoverDelay)}}),[p,u.hoverDelay]);return{preloadRoute:p,createHoverPreloader:f,getStatus:(0,r.useCallback)(()=>({activePreloads:Array.from(m.current),queuedPreloads:[...d.current],strategy:g()}),[g]),clearPreloading:(0,r.useCallback)(()=>{m.current.clear(),d.current=[],h.current&&(cancelIdleCallback(h.current),h.current=null)},[]),isPreloading:m.current.size>0}}},42724:(e,t,a)=>{a.d(t,{G:()=>o,x:()=>l});var r=a(12115),i=a(35695),n=a(37843);let s="rokey_navigation_patterns";function l(){let[e,t]=(0,r.useState)(null),[a,l]=(0,r.useState)([]),o=(0,i.usePathname)(),{prefetchRoute:c}=(0,n.C)(),u=(0,r.useRef)(Date.now()),d=(0,r.useRef)(Date.now());function m(){let e=new Date().getHours();return e>=6&&e<12?"morning":e>=12&&e<17?"afternoon":e>=17&&e<21?"evening":"night"}(0,r.useEffect)(()=>{let e=localStorage.getItem(s);if(e)try{let a=JSON.parse(e);t({...a,sessionStartTime:d.current})}catch(e){}else t({patterns:[],sessionStartTime:d.current,totalNavigations:0,preferredRoutes:[],timeOfDay:m()})},[]);let h=(0,r.useCallback)((a,r)=>{if(!e||a===r)return;let i=Date.now(),n=i-u.current;u.current=i,t(e=>{if(!e)return null;let t=[...e.patterns],l=t.find(e=>e.from===a&&e.to===r);l?(l.frequency+=1,l.lastUsed=i,l.avgTimeSpent=(l.avgTimeSpent+n)/2):t.push({from:a,to:r,frequency:1,lastUsed:i,avgTimeSpent:n});let o=new Map;t.forEach(e=>{o.set(e.to,(o.get(e.to)||0)+e.frequency)});let c=Array.from(o.entries()).sort((e,t)=>t[1]-e[1]).slice(0,5).map(e=>{let[t]=e;return t}),u={...e,patterns:t,totalNavigations:e.totalNavigations+1,preferredRoutes:c,timeOfDay:m()};try{localStorage.setItem(s,JSON.stringify(u))}catch(e){}return u})},[e]),g=(0,r.useCallback)(()=>e&&o?(m(),[...new Set([...e.patterns.filter(e=>e.from===o&&e.frequency>=2).sort((e,t)=>{let a=e.frequency*(1+(Date.now()-e.lastUsed)/864e5);return t.frequency*(1+(Date.now()-t.lastUsed)/864e5)-a}).slice(0,3).map(e=>e.to),...e.patterns.filter(e=>2>=Math.abs(new Date(e.lastUsed).getHours()-new Date().getHours())).sort((e,t)=>t.frequency-e.frequency).slice(0,2).map(e=>e.to)])].slice(0,4)):[],[e,o]);(0,r.useEffect)(()=>{if(e){let e=g();l(e);let t=setTimeout(()=>{e.forEach((e,t)=>{setTimeout(()=>{c(e,{priority:0===t?"high":"low",delay:200*t})},100*t)})},1e3);return()=>clearTimeout(t)}},[e,o,g,c]);let p=(0,r.useRef)(o);(0,r.useEffect)(()=>{p.current&&p.current!==o&&h(p.current,o),p.current=o},[o,h]);let f=(0,r.useCallback)(()=>{if(!e)return[];let t=[];e.preferredRoutes.length>0&&t.push("Most visited: ".concat(e.preferredRoutes[0])),e.totalNavigations>10&&t.push("".concat(e.totalNavigations," total navigations this session"));let a=e.patterns.filter(e=>1>=Math.abs(new Date(e.lastUsed).getHours()-new Date().getHours()));return a.length>0&&t.push("".concat(a.length," patterns match current time")),t},[e]),y=(0,r.useCallback)(()=>{localStorage.removeItem(s),t({patterns:[],sessionStartTime:Date.now(),totalNavigations:0,preferredRoutes:[],timeOfDay:m()}),l([])},[]);return{predictions:a,userBehavior:e,insights:f(),trackNavigation:h,clearPatterns:y,isLearning:null!=e&&!!e.totalNavigations&&e.totalNavigations>5}}function o(){let e=(0,i.usePathname)(),[t,a]=(0,r.useState)([]);return(0,r.useEffect)(()=>{let t=[];switch(e){case"/dashboard":t.push({route:"/playground",reason:"Test your models",priority:"high"},{route:"/my-models",reason:"Manage API keys",priority:"medium"},{route:"/logs",reason:"Check recent activity",priority:"medium"});break;case"/my-models":t.push({route:"/playground",reason:"Test new configuration",priority:"high"},{route:"/routing-setup",reason:"Configure routing",priority:"high"},{route:"/logs",reason:"View API usage",priority:"low"});break;case"/playground":t.push({route:"/logs",reason:"View request details",priority:"medium"},{route:"/my-models",reason:"Switch configuration",priority:"medium"},{route:"/training",reason:"Customize prompts",priority:"low"});break;case"/logs":t.push({route:"/playground",reason:"Test similar requests",priority:"high"},{route:"/analytics",reason:"Detailed analysis",priority:"medium"},{route:"/my-models",reason:"Adjust configuration",priority:"low"})}a(t)},[e]),t}},44042:(e,t,a)=>{a.d(t,{D:()=>i});var r=a(12115);function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{enableMonitoring:a=!0,enableMemoryTracking:i=!0,enableBundleAnalysis:n=!1,enableCacheTracking:s=!0,warningThresholds:l={renderTime:100,memoryUsage:0x3200000,bundleSize:1048576}}=t,[o,c]=(0,r.useState)({renderTime:0}),u=(0,r.useRef)(0),d=(0,r.useRef)(0),m=(0,r.useRef)(0),h=(0,r.useCallback)(()=>{a&&(u.current=performance.now())},[a]),g=(0,r.useCallback)(()=>{if(!a||!u.current)return;let e=performance.now()-u.current;c(t=>({...t,renderTime:e})),l.renderTime,u.current=0},[e,a,l.renderTime]),p=(0,r.useCallback)(()=>{if(!i||!("memory"in performance))return;let e=performance.memory,t={used:e.usedJSHeapSize,total:e.totalJSHeapSize,limit:e.jsHeapSizeLimit};c(e=>({...e,memoryUsage:t})),t.used,l.memoryUsage},[e,i,l.memoryUsage]),f=(0,r.useCallback)(()=>{if(!n)return;let e=performance.getEntriesByType("resource"),t=0;e.forEach(e=>{e.name.includes(".js")&&e.transferSize&&(t+=e.transferSize)}),c(e=>({...e,bundleSize:t})),l.bundleSize},[n,l.bundleSize]),y=(0,r.useCallback)(()=>{if(!s)return;let e=m.current>0?d.current/m.current*100:0;c(t=>({...t,cacheHitRate:e}))},[s]);(0,r.useEffect)(()=>{if(!s)return;let e=e=>{var t,a;(null==(t=e.data)?void 0:t.type)==="CACHE_HIT"?(d.current++,m.current++,y()):(null==(a=e.data)?void 0:a.type)==="CACHE_MISS"&&(m.current++,y())};if("serviceWorker"in navigator)return navigator.serviceWorker.addEventListener("message",e),()=>{navigator.serviceWorker.removeEventListener("message",e)}},[s,y]),(0,r.useEffect)(()=>{if(!a)return;let e=()=>{let e=performance.getEntriesByType("navigation")[0];if(e){let t=e.loadEventEnd-e.startTime;c(e=>({...e,navigationTime:t}))}};if("complete"!==document.readyState)return window.addEventListener("load",e),()=>window.removeEventListener("load",e);e()},[a]),(0,r.useEffect)(()=>{if(!a)return;let e=setInterval(()=>{p(),f()},5e3);return()=>clearInterval(e)},[a,p,f]);let v=(0,r.useCallback)(()=>{let e=[];return o.renderTime>100&&(e.push("Consider memoizing expensive calculations"),e.push("Use React.memo for component optimization"),e.push("Implement virtualization for large lists")),o.memoryUsage&&o.memoryUsage.used>0x3200000&&(e.push("Check for memory leaks"),e.push("Optimize image sizes and formats"),e.push("Implement proper cleanup in useEffect")),o.bundleSize&&o.bundleSize>1048576&&(e.push("Implement code splitting"),e.push("Use dynamic imports for heavy components"),e.push("Remove unused dependencies")),void 0!==o.cacheHitRate&&o.cacheHitRate<70&&(e.push("Improve caching strategy"),e.push("Implement service worker caching"),e.push("Use browser cache headers")),e},[o]),w=(0,r.useCallback)(()=>({component:e,timestamp:new Date().toISOString(),metrics:o,suggestions:v(),userAgent:navigator.userAgent,url:window.location.href}),[e,o,v]);return{metrics:o,startMeasurement:h,endMeasurement:g,trackMemoryUsage:p,analyzeBundleSize:f,trackCacheHitRate:y,getOptimizationSuggestions:v,exportMetrics:w}}},53951:(e,t,a)=>{a.d(t,{c:()=>n});var r=a(12115);let i={};function n(){let[e,t]=(0,r.useState)({}),a=(0,r.useRef)({}),n=(0,r.useCallback)(e=>{let t=i[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),s=(0,r.useCallback)(e=>{let t=i[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete i[e],null):t.data},[]),l=(0,r.useCallback)(async function(e){var r;let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"medium";if(n(e))return s(e);if(null==(r=i[e])?void 0:r.isLoading)return null;a.current[e]&&a.current[e].abort();let o=new AbortController;a.current[e]=o,i[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===l?await new Promise(e=>setTimeout(e,200)):"medium"===l&&await new Promise(e=>setTimeout(e,50));let[a,r,n]=await Promise.allSettled([fetch("/api/custom-configs/".concat(e),{signal:o.signal}),fetch("/api/keys?custom_config_id=".concat(e),{signal:o.signal}),fetch("/api/complexity-assignments?custom_config_id=".concat(e),{signal:o.signal})]),s=null,c=[],u="none",d={},m=[];"fulfilled"===a.status&&a.value.ok&&(u=(s=await a.value.json()).routing_strategy||"none",d=s.routing_strategy_params||{}),"fulfilled"===r.status&&r.value.ok&&(c=await r.value.json()),"fulfilled"===n.status&&n.value.ok&&(m=await n.value.json());let h={configDetails:s,apiKeys:c,routingStrategy:u,routingParams:d,complexityAssignments:m};return i[e]={data:h,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),h}catch(a){if("AbortError"===a.name)return null;return delete i[e],t(t=>({...t,[e]:"error"})),null}finally{delete a.current[e]}},[n,s]),o=(0,r.useCallback)(e=>({onMouseEnter:()=>{n(e)||l(e,"high")}}),[l,n]),c=(0,r.useCallback)(e=>{delete i[e],t(t=>{let a={...t};return delete a[e],a})},[]),u=(0,r.useCallback)(()=>{Object.keys(i).forEach(e=>{delete i[e]}),t({})},[]);return{prefetchRoutingSetupData:l,getCachedData:s,isCached:n,createHoverPrefetch:o,clearCache:c,clearAllCache:u,getStatus:(0,r.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,r.useCallback)(()=>({cachedConfigs:Object.keys(i),cacheSize:Object.keys(i).length,totalCacheAge:Object.values(i).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(i).length}),[]),prefetchStatus:e}}},70036:(e,t,a)=>{a.d(t,{LJ:()=>i});var r=a(12115);function i(e,t){let[a,i]=(0,r.useState)([]),[n,s]=(0,r.useState)(!1),[l,o]=(0,r.useState)(null),[c,u]=(0,r.useState)(null),[d,m]=(0,r.useState)(""),h=(0,r.useRef)(null),g=(0,r.useRef)(null);(0,r.useRef)(null);let p=(0,r.useRef)(0),f=(0,r.useRef)(""),y=(0,r.useRef)(""),v=(0,r.useCallback)(()=>{h.current&&(h.current.close(),h.current=null),g.current&&(clearTimeout(g.current),g.current=null),s(!1)},[]),w=(0,r.useCallback)(()=>{if(!e&&!t)return void o("No execution ID or direct stream URL provided");let a=t||(e?"/api/orchestration/stream/".concat(e):"");if(!a)return void o("No valid stream URL could be determined");if(y.current!==a||!n){v(),f.current=e||"",y.current=a;try{let e=new EventSource(a);h.current=e,e.onopen=()=>{s(!0),o(null),p.current=0},e.onmessage=e=>{try{let t=JSON.parse(e.data);i(e=>[...e,t]),u(t),o(null)}catch(e){o("Error parsing stream data")}},e.addEventListener("orchestration_started",e=>{JSON.parse(e.data)}),e.addEventListener("step_started",e=>{JSON.parse(e.data)}),e.addEventListener("step_progress",e=>{JSON.parse(e.data)}),e.addEventListener("step_completed",e=>{JSON.parse(e.data)}),e.addEventListener("synthesis_started",e=>{JSON.parse(e.data)}),e.addEventListener("orchestration_completed",e=>{JSON.parse(e.data)}),e.onerror=e=>{if(s(!1),p.current<5){let e=1e3*Math.pow(2,p.current);p.current++,o("Connection lost. Reconnecting in ".concat(e/1e3,"s... (attempt ").concat(p.current,"/").concat(5,")")),g.current=setTimeout(()=>{w()},e)}else o("Connection failed after multiple attempts. Please refresh the page.")}}catch(e){o("Failed to establish connection"),s(!1)}}},[e,v]),b=(0,r.useCallback)(()=>{p.current=0,w()},[w]);return(0,r.useEffect)(()=>((e||t)&&w(),()=>{v()}),[e,t,w,v]),(0,r.useEffect)(()=>{let a=()=>{"visible"===document.visibilityState&&!n&&(e||t)&&b()};return document.addEventListener("visibilitychange",a),()=>{document.removeEventListener("visibilitychange",a)}},[n,e,t,b]),(0,r.useEffect)(()=>{let a=()=>{!n&&(e||t)&&b()},r=()=>{o("Network connection lost")};return window.addEventListener("online",a),window.addEventListener("offline",r),()=>{window.removeEventListener("online",a),window.removeEventListener("offline",r)}},[n,e,t,b]),{events:a,isConnected:n,error:l,lastEvent:c,reconnect:b,disconnect:v}}},71118:(e,t,a)=>{a.d(t,{j:()=>s});var r=a(12115),i=a(35695);let n=["/features","/pricing","/about","/auth/signin","/auth/signup","/docs"];function s(){let e=(0,i.useRouter)();return(0,r.useEffect)(()=>{(async()=>{"requestIdleCallback"in window?window.requestIdleCallback(()=>{n.forEach(t=>{e.prefetch(t)})}):setTimeout(()=>{n.forEach(t=>{e.prefetch(t)})},100)})()},[e]),{navigateInstantly:(0,r.useCallback)(t=>{(0,r.startTransition)(()=>{e.push(t)})},[e])}}},87162:(e,t,a)=>{a.d(t,{Z:()=>i});var r=a(12115);function i(){let[e,t]=(0,r.useState)({isOpen:!1,isLoading:!1,title:"",message:"",confirmText:"Confirm",cancelText:"Cancel",type:"danger",onConfirm:()=>{}}),a=(0,r.useCallback)((e,a)=>{t({isOpen:!0,isLoading:!1,title:e.title,message:e.message,confirmText:e.confirmText||"Confirm",cancelText:e.cancelText||"Cancel",type:e.type||"danger",onConfirm:async()=>{t(e=>({...e,isLoading:!0}));try{await a(),t(e=>({...e,isOpen:!1,isLoading:!1}))}catch(e){throw t(e=>({...e,isLoading:!1})),e}}})},[]),i=(0,r.useCallback)(()=>{t(e=>({...e,isOpen:!1,isLoading:!1}))},[]);return{...e,showConfirmation:a,hideConfirmation:i}}},96121:()=>{class e{static getInstance(){return e.instance||(e.instance=new e),e.instance}trackParallelFlow(e){let t="".concat(e.provider,"_").concat(e.model,"_parallel");this.parallelMetrics||(this.parallelMetrics=new Map),this.parallelMetrics.has(t)||this.parallelMetrics.set(t,[]);let a=this.parallelMetrics.get(t);a.push({...e,timestamp:Date.now()}),a.length>this.maxSamples&&a.shift(),e.firstTokenTime}trackMessagingFlow(e){let t="".concat(e.provider,"_").concat(e.model);this.metrics.has(t)||this.metrics.set(t,[]);let a=this.metrics.get(t);a.push({timestamp:Date.now(),...e}),a.length>this.maxSamples&&a.shift(),this.logPerformanceInsights(e)}getStats(e,t){let a="".concat(e,"_").concat(t),r=this.metrics.get(a);if(!r||0===r.length)return null;let i=r.filter(e=>e.success);if(0===i.length)return null;let n=i.map(e=>e.timings.total),s=i.map(e=>e.timings.llmApiCall),l=i.map(e=>e.messageLength);return{provider:e,model:t,sampleCount:i.length,averageTotal:this.calculateAverage(n),averageLLM:this.calculateAverage(s),medianTotal:this.calculateMedian(n),medianLLM:this.calculateMedian(s),p95Total:this.calculatePercentile(n,95),p95LLM:this.calculatePercentile(s,95),minTotal:Math.min(...n),maxTotal:Math.max(...n),averageMessageLength:this.calculateAverage(l),streamingUsage:i.filter(e=>e.isStreaming).length/i.length,errorRate:(r.length-i.length)/r.length,recentTrend:this.calculateTrend(n.slice(-10))}}getSummary(){let e=new Set,t=[];for(let a of this.metrics.keys()){let[r,i]=a.split("_");e.add(r);let n=this.getStats(r,i);n&&t.push(n)}if(0===t.length)return{totalProviders:0,totalModels:0,overallAverageTime:0,fastestProvider:null,slowestProvider:null,recommendations:["No messaging data available yet"]};let a=this.calculateAverage(t.map(e=>e.averageTotal)),r=[...t].sort((e,t)=>e.averageTotal-t.averageTotal);return{totalProviders:e.size,totalModels:t.length,overallAverageTime:a,fastestProvider:r[0],slowestProvider:r[r.length-1],recommendations:this.generateRecommendations(t)}}generateRecommendations(e){let t=[],a=e.filter(e=>e.averageTotal>5e3);a.length>0&&t.push("Consider switching from slow providers: ".concat(a.map(e=>e.provider).join(", "))),e.filter(e=>e.streamingUsage<.5).length>0&&t.push("Enable streaming for better perceived performance");let r=e.filter(e=>e.errorRate>.1);r.length>0&&t.push("High error rates detected for: ".concat(r.map(e=>e.provider).join(", ")));let i=e.filter(e=>e.averageTotal<=2e3);return 0===i.length?t.push("No providers meeting 2s target - consider optimizing or switching providers"):t.push("Fast providers (≤2s): ".concat(i.map(e=>e.provider).join(", "))),t.length>0?t:["Performance looks good!"]}logPerformanceInsights(e){let{provider:t,model:a,timings:r,isStreaming:i,streamingMetrics:n}=e;i&&r.timeToFirstToken&&(r.timeToFirstToken<500||r.timeToFirstToken<1e3||r.timeToFirstToken,n&&n.averageTokenLatency),r.total,r.total,r.total,r.llmApiCall,r.llmApiCall,r.total,r.backendProcessing&&r.frontendProcessing&&(r.backendProcessing,r.total,r.frontendProcessing,r.total),i||r.total}calculateAverage(e){return e.reduce((e,t)=>e+t,0)/e.length}calculateMedian(e){let t=[...e].sort((e,t)=>e-t),a=Math.floor(t.length/2);return t.length%2==0?(t[a-1]+t[a])/2:t[a]}calculatePercentile(e,t){let a=[...e].sort((e,t)=>e-t),r=Math.ceil(t/100*a.length)-1;return a[Math.max(0,r)]}calculateTrend(e){if(e.length<5)return"stable";let t=e.slice(0,Math.floor(e.length/2)),a=e.slice(Math.floor(e.length/2)),r=this.calculateAverage(t),i=(this.calculateAverage(a)-r)/r*100;return i<-10?"improving":i>10?"degrading":"stable"}exportData(){let e={};for(let[t,a]of this.metrics.entries())e[t]=[...a];return e}clear(){this.metrics.clear()}constructor(){this.metrics=new Map,this.maxSamples=50,this.parallelMetrics=new Map}}class t{static getInstance(){return t.instance||(t.instance=new t),t.instance}startRequest(e,t,a){this.timingData.set(e,{requestStart:performance.now(),tokenCount:0,provider:t,model:a})}markFirstToken(e){let t=this.timingData.get(e);if(!t)return null;let a=performance.now()-t.requestStart;return t.firstTokenReceived=performance.now(),a}trackToken(e){let t=this.timingData.get(e);t&&t.tokenCount++}completeStream(e){let t=this.timingData.get(e);if(!t||!t.firstTokenReceived)return null;let a=performance.now();t.streamComplete=a;let r=t.firstTokenReceived-t.requestStart,i=a-t.requestStart,n=a-t.firstTokenReceived,s=t.tokenCount>1?n/(t.tokenCount-1):0,l={timeToFirstToken:r,totalStreamTime:i,totalTokens:t.tokenCount,averageTokenLatency:s};return this.timingData.delete(e),l}getStatus(e){let t=this.timingData.get(e);return t?t.firstTokenReceived?t.streamComplete?"Complete":"Streaming in progress":"Waiting for first token":"Not tracked"}clear(){this.timingData.clear()}constructor(){this.timingData=new Map}}let a=e.getInstance(),r=t.getInstance();function i(){let e=a.getSummary();e.fastestProvider,e.slowestProvider,e.recommendations.forEach(e=>console.log("   • ".concat(e)))}function n(){var e;null==(e=r.timingData)||e.size}function s(){}function l(){let e=setInterval(()=>{a.getSummary().totalProviders},3e4);globalThis.__performanceMonitoringInterval=e}function o(){let e=globalThis.__performanceMonitoringInterval;e&&(clearInterval(e),delete globalThis.__performanceMonitoringInterval)}function c(){let e=a.getSummary();0!==e.totalProviders&&(e.overallAverageTime<2e3||e.overallAverageTime<5e3||e.overallAverageTime,e.fastestProvider,e.slowestProvider)}"undefined"!=typeof globalThis&&(globalThis.logComprehensivePerformanceReport=i,globalThis.logFirstTokenReport=n,globalThis.logGoogleStreamingDebug=s,globalThis.quickPerformanceCheck=c,globalThis.startPerformanceMonitoring=l,globalThis.stopPerformanceMonitoring=o,globalThis.performanceLogs={comprehensive:i,firstToken:n,googleDebug:s,quick:c,startMonitoring:l,stopMonitoring:o})}}]);