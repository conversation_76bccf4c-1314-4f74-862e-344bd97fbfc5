"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelServiceClient = void 0;
const jsonProtos = require("../../protos/protos.json");
const google_gax_1 = require("google-gax");
/**
 * Client JSON configuration object, loaded from
 * `src/v1beta3/model_service_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./model_service_client_config.json");
const version = require('../../../package.json').version;
/**
 *  Provides methods for getting metadata information about Generative Models.
 * @class
 * @memberof v1beta3
 */
class ModelServiceClient {
    _terminated = false;
    _opts;
    _providedCustomServicePath;
    _gaxModule;
    _gaxGrpc;
    _protos;
    _defaults;
    _universeDomain;
    _servicePath;
    _log = google_gax_1.loggingUtils.log('generativelanguage');
    auth;
    descriptors = {
        page: {},
        stream: {},
        longrunning: {},
        batching: {},
    };
    warn;
    innerApiCalls;
    pathTemplates;
    operationsClient;
    modelServiceStub;
    /**
     * Construct an instance of ModelServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new ModelServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if (opts?.universe_domain &&
            opts?.universeDomain &&
            opts?.universe_domain !== opts?.universeDomain) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            opts?.universeDomain ??
                opts?.universe_domain ??
                universeDomainEnvVar ??
                'googleapis.com';
        this._servicePath = 'generativelanguage.' + this._universeDomain;
        const servicePath = opts?.servicePath || opts?.apiEndpoint || this._servicePath;
        this._providedCustomServicePath = !!(opts?.servicePath || opts?.apiEndpoint);
        const port = opts?.port || staticMembers.port;
        const clientConfig = opts?.clientConfig ?? {};
        const fallback = opts?.fallback ??
            (typeof window !== 'undefined' && typeof window?.fetch === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            modelPathTemplate: new this._gaxModule.PathTemplate('models/{model}'),
            permissionPathTemplate: new this._gaxModule.PathTemplate('tunedModels/{tuned_model}/permissions/{permission}'),
            tunedModelPathTemplate: new this._gaxModule.PathTemplate('tunedModels/{tuned_model}'),
        };
        // Some of the methods on this service return "paged" results,
        // (e.g. 50 results at a time, with tokens to get subsequent
        // pages). Denote the keys used for pagination and results.
        this.descriptors.page = {
            listModels: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'models'),
            listTunedModels: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'tunedModels'),
        };
        const protoFilesRoot = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
        // This API contains "long-running operations", which return a
        // an Operation object that allows for tracking of the operation,
        // rather than holding a request open.
        const lroOptions = {
            auth: this.auth,
            grpc: 'grpc' in this._gaxGrpc ? this._gaxGrpc.grpc : undefined,
        };
        if (opts.fallback) {
            lroOptions.protoJson = protoFilesRoot;
            lroOptions.httpRules = [];
        }
        this.operationsClient = this._gaxModule
            .lro(lroOptions)
            .operationsClient(opts);
        const createTunedModelResponse = protoFilesRoot.lookup('.google.ai.generativelanguage.v1beta3.TunedModel');
        const createTunedModelMetadata = protoFilesRoot.lookup('.google.ai.generativelanguage.v1beta3.CreateTunedModelMetadata');
        this.descriptors.longrunning = {
            createTunedModel: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createTunedModelResponse.decode.bind(createTunedModelResponse), createTunedModelMetadata.decode.bind(createTunedModelMetadata)),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.ai.generativelanguage.v1beta3.ModelService', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.modelServiceStub) {
            return this.modelServiceStub;
        }
        // Put together the "service stub" for
        // google.ai.generativelanguage.v1beta3.ModelService.
        this.modelServiceStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.ai.generativelanguage.v1beta3.ModelService')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.ai.generativelanguage.v1beta3
                    .ModelService, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const modelServiceStubMethods = [
            'getModel',
            'listModels',
            'getTunedModel',
            'listTunedModels',
            'createTunedModel',
            'updateTunedModel',
            'deleteTunedModel',
        ];
        for (const methodName of modelServiceStubMethods) {
            const callPromise = this.modelServiceStub.then(stub => (...args) => {
                if (this._terminated) {
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.page[methodName] ||
                this.descriptors.longrunning[methodName] ||
                undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.modelServiceStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'generativelanguage.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'generativelanguage.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return [];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    getModel(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('getModel request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getModel response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .getModel(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getModel response %j', response);
            return [response, options, rawResponse];
        });
    }
    getTunedModel(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('getTunedModel request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getTunedModel response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .getTunedModel(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getTunedModel response %j', response);
            return [response, options, rawResponse];
        });
    }
    updateTunedModel(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'tuned_model.name': request.tunedModel.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('updateTunedModel request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('updateTunedModel response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .updateTunedModel(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('updateTunedModel response %j', response);
            return [response, options, rawResponse];
        });
    }
    deleteTunedModel(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('deleteTunedModel request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('deleteTunedModel response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .deleteTunedModel(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('deleteTunedModel response %j', response);
            return [response, options, rawResponse];
        });
    }
    createTunedModel(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('createTunedModel response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('createTunedModel request %j', request);
        return this.innerApiCalls
            .createTunedModel(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('createTunedModel response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `createTunedModel()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta3/model_service.create_tuned_model.js</caption>
     * region_tag:generativelanguage_v1beta3_generated_ModelService_CreateTunedModel_async
     */
    async checkCreateTunedModelProgress(name) {
        this._log.info('createTunedModel long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createTunedModel, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    listModels(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listModels values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listModels request %j', request);
        return this.innerApiCalls
            .listModels(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listModels values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listModels`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} request.pageSize
     *   The maximum number of `Models` to return (per page).
     *
     *   The service may return fewer models.
     *   If unspecified, at most 50 models will be returned per page.
     *   This method returns at most 1000 models per page, even if you pass a larger
     *   page_size.
     * @param {string} request.pageToken
     *   A page token, received from a previous `ListModels` call.
     *
     *   Provide the `page_token` returned by one request as an argument to the next
     *   request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListModels` must match
     *   the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1beta3.Model|Model} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listModelsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listModelsStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        const defaultCallSettings = this._defaults['listModels'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listModels stream %j', request);
        return this.descriptors.page.listModels.createStream(this.innerApiCalls.listModels, request, callSettings);
    }
    /**
     * Equivalent to `listModels`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} request.pageSize
     *   The maximum number of `Models` to return (per page).
     *
     *   The service may return fewer models.
     *   If unspecified, at most 50 models will be returned per page.
     *   This method returns at most 1000 models per page, even if you pass a larger
     *   page_size.
     * @param {string} request.pageToken
     *   A page token, received from a previous `ListModels` call.
     *
     *   Provide the `page_token` returned by one request as an argument to the next
     *   request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListModels` must match
     *   the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1beta3.Model|Model}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta3/model_service.list_models.js</caption>
     * region_tag:generativelanguage_v1beta3_generated_ModelService_ListModels_async
     */
    listModelsAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        const defaultCallSettings = this._defaults['listModels'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listModels iterate %j', request);
        return this.descriptors.page.listModels.asyncIterate(this.innerApiCalls['listModels'], request, callSettings);
    }
    listTunedModels(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listTunedModels values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listTunedModels request %j', request);
        return this.innerApiCalls
            .listTunedModels(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listTunedModels values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listTunedModels`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `TunedModels` to return (per page).
     *   The service may return fewer tuned models.
     *
     *   If unspecified, at most 10 tuned models will be returned.
     *   This method returns at most 1000 models per page, even if you pass a larger
     *   page_size.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListTunedModels` call.
     *
     *   Provide the `page_token` returned by one request as an argument to the next
     *   request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListTunedModels`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1beta3.TunedModel|TunedModel} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listTunedModelsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listTunedModelsStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        const defaultCallSettings = this._defaults['listTunedModels'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listTunedModels stream %j', request);
        return this.descriptors.page.listTunedModels.createStream(this.innerApiCalls.listTunedModels, request, callSettings);
    }
    /**
     * Equivalent to `listTunedModels`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `TunedModels` to return (per page).
     *   The service may return fewer tuned models.
     *
     *   If unspecified, at most 10 tuned models will be returned.
     *   This method returns at most 1000 models per page, even if you pass a larger
     *   page_size.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListTunedModels` call.
     *
     *   Provide the `page_token` returned by one request as an argument to the next
     *   request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListTunedModels`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1beta3.TunedModel|TunedModel}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta3/model_service.list_tuned_models.js</caption>
     * region_tag:generativelanguage_v1beta3_generated_ModelService_ListTunedModels_async
     */
    listTunedModelsAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        const defaultCallSettings = this._defaults['listTunedModels'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listTunedModels iterate %j', request);
        return this.descriptors.page.listTunedModels.asyncIterate(this.innerApiCalls['listTunedModels'], request, callSettings);
    }
    /**
     * Gets the latest state of a long-running operation.  Clients can use this
     * method to poll the operation result at intervals as recommended by the API
     * service.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     *   for the details.
     * @param {function(?Error, ?Object)=} callback
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing
     *   {@link google.longrunning.Operation | google.longrunning.Operation}.
     * @return {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     * {@link google.longrunning.Operation | google.longrunning.Operation}.
     * The promise has a method named "cancel" which cancels the ongoing API call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * const name = '';
     * const [response] = await client.getOperation({name});
     * // doThingsWith(response)
     * ```
     */
    getOperation(request, optionsOrCallback, callback) {
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.getOperation(request, options, callback);
    }
    /**
     * Lists operations that match the specified filter in the request. If the
     * server doesn't support this method, it returns `UNIMPLEMENTED`. Returns an iterable object.
     *
     * For-await-of syntax is used with the iterable to recursively get response element on-demand.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation collection.
     * @param {string} request.filter - The standard list filter.
     * @param {number=} request.pageSize -
     *   The maximum number of resources contained in the underlying API
     *   response. If page streaming is performed per-resource, this
     *   parameter does not affect the return value. If page streaming is
     *   performed per-page, this determines the maximum number of
     *   resources in a page.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     *   details.
     * @returns {Object}
     *   An iterable Object that conforms to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | iteration protocols}.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * for await (const response of client.listOperationsAsync(request));
     * // doThingsWith(response)
     * ```
     */
    listOperationsAsync(request, options) {
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.listOperationsAsync(request, options);
    }
    /**
     * Starts asynchronous cancellation on a long-running operation.  The server
     * makes a best effort to cancel the operation, but success is not
     * guaranteed.  If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.  Clients can use
     * {@link Operations.GetOperation} or
     * other methods to check whether the cancellation succeeded or whether the
     * operation completed despite cancellation. On successful cancellation,
     * the operation is not deleted; instead, it becomes an operation with
     * an {@link Operation.error} value with a {@link google.rpc.Status.code} of
     * 1, corresponding to `Code.CANCELLED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be cancelled.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     * details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.cancelOperation({name: ''});
     * ```
     */
    cancelOperation(request, optionsOrCallback, callback) {
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.cancelOperation(request, options, callback);
    }
    /**
     * Deletes a long-running operation. This method indicates that the client is
     * no longer interested in the operation result. It does not cancel the
     * operation. If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be deleted.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     * for the details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.deleteOperation({name: ''});
     * ```
     */
    deleteOperation(request, optionsOrCallback, callback) {
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.deleteOperation(request, options, callback);
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(model) {
        return this.pathTemplates.modelPathTemplate.render({
            model: model,
        });
    }
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).model;
    }
    /**
     * Return a fully-qualified permission resource name string.
     *
     * @param {string} tuned_model
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    permissionPath(tunedModel, permission) {
        return this.pathTemplates.permissionPathTemplate.render({
            tuned_model: tunedModel,
            permission: permission,
        });
    }
    /**
     * Parse the tuned_model from Permission resource.
     *
     * @param {string} permissionName
     *   A fully-qualified path representing Permission resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromPermissionName(permissionName) {
        return this.pathTemplates.permissionPathTemplate.match(permissionName)
            .tuned_model;
    }
    /**
     * Parse the permission from Permission resource.
     *
     * @param {string} permissionName
     *   A fully-qualified path representing Permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromPermissionName(permissionName) {
        return this.pathTemplates.permissionPathTemplate.match(permissionName)
            .permission;
    }
    /**
     * Return a fully-qualified tunedModel resource name string.
     *
     * @param {string} tuned_model
     * @returns {string} Resource name string.
     */
    tunedModelPath(tunedModel) {
        return this.pathTemplates.tunedModelPathTemplate.render({
            tuned_model: tunedModel,
        });
    }
    /**
     * Parse the tuned_model from TunedModel resource.
     *
     * @param {string} tunedModelName
     *   A fully-qualified path representing TunedModel resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelName(tunedModelName) {
        return this.pathTemplates.tunedModelPathTemplate.match(tunedModelName)
            .tuned_model;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.modelServiceStub && !this._terminated) {
            return this.modelServiceStub.then(stub => {
                this._log.info('ending gRPC channel');
                this._terminated = true;
                stub.close();
                void this.operationsClient.close();
            });
        }
        return Promise.resolve();
    }
}
exports.ModelServiceClient = ModelServiceClient;
//# sourceMappingURL=model_service_client.js.map