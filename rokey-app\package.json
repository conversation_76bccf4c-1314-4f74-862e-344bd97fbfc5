{"name": "rokey-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:clean": "node clear-dev-cache.js && next dev", "prebuild": "npm run lint", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "eslint .", "clear-cache": "node clear-dev-cache.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@google-ai/generativelanguage": "^3.2.0", "@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/line-clamp": "^0.4.4", "@vercel/speed-insights": "^1.2.0", "crypto-js": "^4.2.0", "framer-motion": "^12.18.1", "google-auth-library": "^10.0.0-rc.2", "mammoth": "^1.9.1", "next": "^15.0.3", "openai": "^5.0.1", "pdf-parse": "^1.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "react-tooltip": "^5.28.1", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "stripe": "^17.7.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/compat": "^1.2.0", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.15.0", "@next/bundle-analyzer": "^15.3.3", "@tailwindcss/postcss": "^4", "@types/node": "^22", "@types/pdf-parse": "^1.1.5", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9.15.0", "eslint-config-next": "^15.3.3", "eslint-plugin-react-hooks": "^5.0.0", "ignore-loader": "^0.1.2", "tailwindcss": "^4", "typescript": "^5", "typescript-eslint": "^8.34.0"}, "overrides": {"eslint": "$eslint"}}