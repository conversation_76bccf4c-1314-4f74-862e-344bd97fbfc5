import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { decrypt } from '@/lib/encryption';
import { z } from 'zod';
import { EnhancedModerator } from '@/utils/moderatorUtils';
import { broadcastOrchestrationEvent } from '@/utils/orchestrationUtils';
import { generateConversationalMessage } from '@/utils/orchestrationUtils';

// Timeout configuration for orchestration
const TIMEOUT_CONFIG = {
  CLASSIFICATION: 5000,    // 5s for role classification
  LLM_REQUEST: 8000,      // 8s for LLM requests
  CONNECTION: 2000,       // 2s for connection establishment
  SOCKET: 1500,          // 1.5s for socket timeout
  GOOGLE_CLASSIFICATION: 7000, // 7s specifically for Google classification
};



// Try using a simpler approach - just regular fetch with basic retry
async function robustFetch(
  url: string,
  options: RequestInit,
  maxRetries: number = 3,
  timeoutMs?: number
): Promise<Response> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`[Orchestration RobustFetch] Attempt ${attempt}/${maxRetries} for ${url}`);

      // Try with just basic fetch first
      const response = await fetch(url, options);

      console.log(`[Orchestration RobustFetch] Success on attempt ${attempt} for ${url} - Status: ${response.status}`);
      return response;

    } catch (error: any) {
      lastError = error;
      console.warn(`[Orchestration RobustFetch] Attempt ${attempt}/${maxRetries} failed for ${url}:`, error.message);
      console.warn(`[Orchestration RobustFetch] Error details:`, error);

      if (attempt === maxRetries) {
        console.error(`[Orchestration RobustFetch] All ${maxRetries} attempts failed for ${url}`);
        throw new Error(error.message);
      }

      const backoffMs = 100 * attempt;
      await new Promise(resolve => setTimeout(resolve, backoffMs));
    }
  }

  throw lastError;
}

// Enhanced executeProviderRequest with streaming support for orchestration
async function executeProviderRequest(
  providerName: string | null,
  modelIdInDb: string | null,
  apiKeyToUse: string,
  requestPayload: any,
  onStreamChunk?: (chunk: string) => void
): Promise<any> {
  const p_llmRequestTimestamp = new Date();
  let p_llmResponseTimestamp = new Date();
  let p_status: number | undefined = undefined;
  let p_error: any = undefined;
  let p_responseData: any = undefined;

  try {
    // Determine the actual model ID to be used for the provider API call
    const actualModelIdForProvider = getDirectProviderModelId(modelIdInDb, providerName || '');
    const effectiveModelId = providerName?.toLowerCase() === 'openrouter' ? modelIdInDb : actualModelIdForProvider;

    console.log(`[Orchestration Provider] Calling Provider: ${providerName}, Model: ${effectiveModelId}, Streaming: ${requestPayload.stream}`);

    if (providerName?.toLowerCase() === 'openai') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload = { ...providerAPIPayload, model: effectiveModelId, messages: requestPayload.messages, stream: requestPayload.stream };
      Object.keys(payload).forEach((key: string) => (payload as any)[key] === undefined && delete (payload as any)[key]);

      const openaiOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyToUse}`,
          'Origin': 'https://rokey.app',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify(payload)
      };

      console.log(`[OpenAI Orchestration] Attempting connection to OpenAI API...`);
      const rawResponse = await robustFetch('https://api.openai.com/v1/chat/completions', openaiOptions);
      p_llmResponseTimestamp = new Date();
      p_status = rawResponse.status;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `OpenAI Error: ${err?.error?.message || rawResponse.statusText}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }

      // Handle streaming response
      if (requestPayload.stream && rawResponse.body && onStreamChunk) {
        const reader = rawResponse.body.getReader();
        const decoder = new TextDecoder();
        let accumulatedResponse = '';
        let buffer = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content || '';

                  if (content) {
                    accumulatedResponse += content;
                    onStreamChunk(content);
                  }

                  // Update usage info if available
                  if (parsed.usage) {
                    p_responseData = {
                      ...p_responseData,
                      usage: parsed.usage,
                      choices: [{
                        message: { content: accumulatedResponse },
                        finish_reason: parsed.choices?.[0]?.finish_reason || null
                      }]
                    };
                  }
                } catch (parseError) {
                  console.warn(`[OpenAI Orchestration] Error parsing streaming data: ${parseError}`);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }

        // Ensure we have a complete response object
        if (!p_responseData) {
          p_responseData = {
            choices: [{
              message: { content: accumulatedResponse },
              finish_reason: 'stop'
            }],
            usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
          };
        }
      } else {
        p_responseData = await rawResponse.json();
      }

    } else if (providerName?.toLowerCase() === 'openrouter') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload = {
        ...providerAPIPayload,
        model: effectiveModelId,
        messages: requestPayload.messages,
        stream: requestPayload.stream,
        usage: { include: true }
      };

      const openrouterOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyToUse}`,
          'HTTP-Referer': 'https://rokey.app',
          'X-Title': 'RoKey Orchestration',
          'User-Agent': 'RoKey/1.0 (Orchestration)',
        },
        body: JSON.stringify(payload)
      };

      console.log(`[OpenRouter Orchestration] Attempting connection to OpenRouter API...`);
      const rawResponse = await robustFetch('https://openrouter.ai/api/v1/chat/completions', openrouterOptions);
      p_llmResponseTimestamp = new Date();
      p_status = rawResponse.status;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `OpenRouter Error: ${err?.error?.message || rawResponse.statusText}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }

      // Handle streaming response for OpenRouter
      if (requestPayload.stream && rawResponse.body && onStreamChunk) {
        const reader = rawResponse.body.getReader();
        const decoder = new TextDecoder();
        let accumulatedResponse = '';
        let buffer = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content || '';

                  if (content) {
                    accumulatedResponse += content;
                    onStreamChunk(content);
                  }

                  // Update usage info if available
                  if (parsed.usage) {
                    p_responseData = {
                      ...p_responseData,
                      usage: parsed.usage,
                      choices: [{
                        message: { content: accumulatedResponse },
                        finish_reason: parsed.choices?.[0]?.finish_reason || null
                      }]
                    };
                  }
                } catch (parseError) {
                  console.warn(`[OpenRouter Orchestration] Error parsing streaming data: ${parseError}`);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }

        // Ensure we have a complete response object
        if (!p_responseData) {
          p_responseData = {
            choices: [{
              message: { content: accumulatedResponse },
              finish_reason: 'stop'
            }],
            usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
          };
        }
      } else {
        p_responseData = await rawResponse.json();
      }

    } else if (providerName?.toLowerCase() === 'google') {
      // Transform model ID: remove 'models/' prefix for OpenAI endpoint
      const openAIModelId = effectiveModelId?.replace(/^models\//, '') || effectiveModelId;
      const googleApiUrl = `https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`;

      // Convert messages to OpenAI format (same as main route)
      const openAIMessages = requestPayload.messages.map((msg: any) => {
        if (typeof msg.content === 'string') {
          return { role: msg.role, content: msg.content };
        }
        if (Array.isArray(msg.content)) {
          // Handle multimodal content for OpenAI format
          const openAIContent = msg.content.map((part: any) => {
            if (part.type === 'text' && typeof part.text === 'string') {
              return { type: 'text', text: part.text };
            }
            if (part.type === 'image_url' && part.image_url?.url) {
              return { type: 'image_url', image_url: { url: part.image_url.url } };
            }
            return null;
          }).filter(Boolean);
          return { role: msg.role, content: openAIContent };
        }
        return { role: msg.role, content: "[RoKey: Invalid content structure for Google]" };
      });

      if (openAIMessages.length === 0) {
        p_error = { message: 'No processable message content found for Google provider after filtering.', status: 400 };
        throw p_error;
      }

      const googlePayload: Record<string, any> = {
        model: openAIModelId,
        messages: openAIMessages,
        stream: requestPayload.stream || false
      };

      if (requestPayload.temperature !== undefined) googlePayload.temperature = requestPayload.temperature;
      if (requestPayload.max_tokens !== undefined) googlePayload.max_tokens = requestPayload.max_tokens;
      if (requestPayload.top_p !== undefined) googlePayload.top_p = requestPayload.top_p;

      const googleFetchOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyToUse}`,
          'User-Agent': 'RoKey/1.0 (Orchestration)',
          'Origin': 'https://rokey.app',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify(googlePayload)
      };

      console.log(`[Google Orchestration] Attempting connection to Google Gemini OpenAI-compatible API...`);
      console.log(`[Google Orchestration DEBUG] URL: ${googleApiUrl}`);
      console.log(`[Google Orchestration DEBUG] Headers:`, JSON.stringify(googleFetchOptions.headers, null, 2));
      console.log(`[Google Orchestration DEBUG] Payload:`, JSON.stringify(googlePayload, null, 2));
      console.log(`[Google Orchestration DEBUG] API Key (first 10 chars): ${apiKeyToUse.substring(0, 10)}...`);

      const rawResponse = await robustFetch(googleApiUrl, googleFetchOptions);
      p_llmResponseTimestamp = new Date();
      p_status = rawResponse.status;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        let errMsg = err?.error?.message || rawResponse.statusText;
        if (Array.isArray(err) && err[0]?.error?.message) errMsg = err[0].error.message;

        // Check if this is a retryable error (503 Service Unavailable, rate limits, etc.)
        if (rawResponse.status === 503 || rawResponse.status === 429 || errMsg.includes('overloaded') || errMsg.includes('rate limit')) {
          console.log(`[Google Orchestration] Retryable error (${rawResponse.status}): ${errMsg}. Will retry with exponential backoff.`);
          p_error = { message: `Google Error: ${errMsg}`, status: rawResponse.status, provider_error: err, retryable: true };
        } else {
          p_error = { message: `Google Error: ${errMsg}`, status: rawResponse.status, provider_error: err };
        }
        throw p_error;
      }

      // Handle streaming response for Google
      if (requestPayload.stream && rawResponse.body && onStreamChunk) {
        const reader = rawResponse.body.getReader();
        const decoder = new TextDecoder();
        let accumulatedResponse = '';
        let buffer = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content || '';

                  if (content) {
                    accumulatedResponse += content;
                    onStreamChunk(content);
                  }

                  // Update usage info if available
                  if (parsed.usage) {
                    p_responseData = {
                      ...p_responseData,
                      usage: parsed.usage,
                      choices: [{
                        message: { content: accumulatedResponse },
                        finish_reason: parsed.choices?.[0]?.finish_reason || null
                      }]
                    };
                  }
                } catch (parseError) {
                  console.warn(`[Google Orchestration] Error parsing streaming data: ${parseError}`);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }

        // Ensure we have a complete response object
        if (!p_responseData) {
          p_responseData = {
            choices: [{
              message: { content: accumulatedResponse },
              finish_reason: 'stop'
            }],
            usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
          };
        }
      } else {
        p_responseData = await rawResponse.json();
      }

    } else if (providerName?.toLowerCase() === 'anthropic') {
      const maxTokens = requestPayload.max_tokens || 2048;
      let systemPrompt;
      const anthropicMessages = requestPayload.messages.filter((msg: any) => {
        if (msg.role === 'system') {
          if(typeof msg.content === 'string') systemPrompt = msg.content;
          return false;
        }
        return true;
      }).map((msg: any) => ({ role: msg.role as 'user'|'assistant', content: msg.content }));

      if (anthropicMessages.length === 0 || anthropicMessages[0].role !== 'user') {
        p_error = { message: 'Invalid messages format for Anthropic: Must contain at least one user message and start with user after system filter.', status: 400 };
        throw p_error;
      }

      const payload: any = {
        model: effectiveModelId,
        messages: anthropicMessages,
        max_tokens: maxTokens,
        stream: requestPayload.stream
      };
      if (systemPrompt) payload.system = systemPrompt;
      if (requestPayload.temperature !== undefined) payload.temperature = requestPayload.temperature;
      if (requestPayload.top_p !== undefined) payload.top_p = requestPayload.top_p;

      const anthropicOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': apiKeyToUse,
          'anthropic-version': '2023-06-01',
          'User-Agent': 'RoKey/1.0 (Orchestration)',
        },
        body: JSON.stringify(payload)
      };

      console.log(`[Anthropic Orchestration] Attempting connection to Anthropic API...`);
      const rawResponse = await robustFetch('https://api.anthropic.com/v1/messages', anthropicOptions);
      p_llmResponseTimestamp = new Date();
      p_status = rawResponse.status;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `Anthropic Error: ${err?.error?.message || rawResponse.statusText}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }

      // Handle streaming response for Anthropic
      if (requestPayload.stream && rawResponse.body && onStreamChunk) {
        const reader = rawResponse.body.getReader();
        const decoder = new TextDecoder();
        let accumulatedResponse = '';
        let buffer = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.delta?.text || '';

                  if (content) {
                    accumulatedResponse += content;
                    onStreamChunk(content);
                  }

                  // Update usage info if available
                  if (parsed.usage) {
                    p_responseData = {
                      id: parsed.id,
                      object: 'chat.completion',
                      created: Math.floor(Date.now() / 1000),
                      model: effectiveModelId,
                      choices: [{
                        index: 0,
                        message: { role: "assistant", content: accumulatedResponse },
                        finish_reason: parsed.delta?.stop_reason || 'stop'
                      }],
                      usage: {
                        prompt_tokens: parsed.usage?.input_tokens || 0,
                        completion_tokens: parsed.usage?.output_tokens || 0,
                        total_tokens: (parsed.usage?.input_tokens || 0) + (parsed.usage?.output_tokens || 0)
                      }
                    };
                  }
                } catch (parseError) {
                  console.warn(`[Anthropic Orchestration] Error parsing streaming data: ${parseError}`);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }

        // Ensure we have a complete response object
        if (!p_responseData) {
          p_responseData = {
            id: `anthropic-${Date.now()}`,
            object: 'chat.completion',
            created: Math.floor(Date.now() / 1000),
            model: effectiveModelId,
            choices: [{
              index: 0,
              message: { role: "assistant", content: accumulatedResponse },
              finish_reason: 'stop'
            }],
            usage: {
              prompt_tokens: 0,
              completion_tokens: 0,
              total_tokens: 0
            }
          };
        }
      } else {
        const anthropicData = await rawResponse.json();
        const textResponse = anthropicData.content?.[0]?.text || '';

        // Transform to OpenAI-compatible format
        p_responseData = {
          id: anthropicData.id,
          object: 'chat.completion',
          created: Math.floor(Date.now() / 1000),
          model: effectiveModelId,
          choices: [{
            index: 0,
            message: { role: "assistant", content: textResponse },
            finish_reason: anthropicData.stop_reason?.toLowerCase() || 'stop'
          }],
          usage: {
            prompt_tokens: anthropicData.usage?.input_tokens,
            completion_tokens: anthropicData.usage?.output_tokens,
            total_tokens: (anthropicData.usage?.input_tokens || 0) + (anthropicData.usage?.output_tokens || 0)
          }
        };
      }

    } else if (providerName?.toLowerCase() === 'deepseek') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload = { ...providerAPIPayload, model: effectiveModelId, messages: requestPayload.messages, stream: requestPayload.stream };
      Object.keys(payload).forEach((key: string) => (payload as any)[key] === undefined && delete (payload as any)[key]);

      const deepseekOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyToUse}`,
          'User-Agent': 'RoKey/1.0 (Orchestration)',
        },
        body: JSON.stringify(payload)
      };

      console.log(`[DeepSeek Orchestration] Attempting connection to DeepSeek API...`);
      const rawResponse = await robustFetch('https://api.deepseek.com/v1/chat/completions', deepseekOptions);
      p_llmResponseTimestamp = new Date();
      p_status = rawResponse.status;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `DeepSeek Error: ${err?.error?.message || rawResponse.statusText}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }

      // Handle streaming response for DeepSeek
      if (requestPayload.stream && rawResponse.body && onStreamChunk) {
        const reader = rawResponse.body.getReader();
        const decoder = new TextDecoder();
        let accumulatedResponse = '';
        let buffer = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content || '';

                  if (content) {
                    accumulatedResponse += content;
                    onStreamChunk(content);
                  }

                  // Update usage info if available
                  if (parsed.usage) {
                    p_responseData = {
                      ...p_responseData,
                      usage: parsed.usage,
                      choices: [{
                        message: { content: accumulatedResponse },
                        finish_reason: parsed.choices?.[0]?.finish_reason || null
                      }]
                    };
                  }
                } catch (parseError) {
                  console.warn(`[DeepSeek Orchestration] Error parsing streaming data: ${parseError}`);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }

        // Ensure we have a complete response object
        if (!p_responseData) {
          p_responseData = {
            choices: [{
              message: { content: accumulatedResponse },
              finish_reason: 'stop'
            }],
            usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
          };
        }
      } else {
        p_responseData = await rawResponse.json();
      }

    } else if (providerName?.toLowerCase() === 'xai') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload: Record<string, any> = {
        ...providerAPIPayload,
        model: effectiveModelId,
        messages: requestPayload.messages,
        stream: requestPayload.stream || false
      };
      if (typeof requestPayload.temperature === 'number') payload.temperature = requestPayload.temperature;
      if (typeof requestPayload.max_tokens === 'number') payload.max_tokens = requestPayload.max_tokens;
      if (typeof requestPayload.top_p === 'number') payload.top_p = requestPayload.top_p;

      const xaiOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyToUse}`,
          'User-Agent': 'RoKey/1.0 (Orchestration)',
        },
        body: JSON.stringify(payload)
      };

      console.log(`[XAI Orchestration] Attempting connection to XAI API...`);
      const rawResponse = await robustFetch('https://api.x.ai/v1/chat/completions', xaiOptions);
      p_llmResponseTimestamp = new Date();
      p_status = rawResponse.status;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `XAI Error: ${err?.error?.message || rawResponse.statusText}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }

      // Handle streaming response for XAI
      if (requestPayload.stream && rawResponse.body && onStreamChunk) {
        const reader = rawResponse.body.getReader();
        const decoder = new TextDecoder();
        let accumulatedResponse = '';
        let buffer = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content || '';

                  if (content) {
                    accumulatedResponse += content;
                    onStreamChunk(content);
                  }

                  // Update usage info if available
                  if (parsed.usage) {
                    p_responseData = {
                      ...p_responseData,
                      usage: parsed.usage,
                      choices: [{
                        message: { content: accumulatedResponse },
                        finish_reason: parsed.choices?.[0]?.finish_reason || null
                      }]
                    };
                  }
                } catch (parseError) {
                  console.warn(`[XAI Orchestration] Error parsing streaming data: ${parseError}`);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }

        // Ensure we have a complete response object
        if (!p_responseData) {
          p_responseData = {
            choices: [{
              message: { content: accumulatedResponse },
              finish_reason: 'stop'
            }],
            usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
          };
        }
      } else {
        p_responseData = await rawResponse.json();
      }

    } else {
      throw new Error(`Unsupported provider: ${providerName}`);
    }

    return {
      success: true,
      response: undefined,
      responseData: p_responseData,
      status: p_status,
      error: p_error,
      llmRequestTimestamp: p_llmRequestTimestamp,
      llmResponseTimestamp: p_llmResponseTimestamp
    };

  } catch (error: any) {
    console.error(`[Orchestration Provider] Error: ${error}`);
    return {
      success: false,
      response: undefined,
      responseData: undefined,
      status: p_status || 500,
      error: error,
      llmRequestTimestamp: p_llmRequestTimestamp,
      llmResponseTimestamp: p_llmResponseTimestamp
    };
  }
}

// Helper function to get the actual model ID for direct provider calls (same as main system)
function getDirectProviderModelId(modelIdFromDb: string | null, providerName: string): string {
  if (!modelIdFromDb) return '';
  const lowerProviderName = providerName.toLowerCase();
  const prefix = `${lowerProviderName}/`;
  if (modelIdFromDb.toLowerCase().startsWith(prefix)) return modelIdFromDb.substring(prefix.length);
  return modelIdFromDb;
}

// Schema for the request body
const ProcessStepSchema = z.object({
  executionId: z.string().uuid(),
  stepId: z.string().uuid(),
});

export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();
  
  try {
    // Parse and validate the request body
    const body = await request.json();
    const validatedBody = ProcessStepSchema.parse(body);
    
    // Get the step to process
    const { data: step, error: stepError } = await supabase
      .from('orchestration_steps')
      .select('*')
      .eq('id', validatedBody.stepId)
      .eq('execution_id', validatedBody.executionId)
      .single();
      
    if (stepError || !step) {
      return NextResponse.json(
        { error: 'Step not found' },
        { status: 404 }
      );
    }
    
    // Check if the step is in a processable state
    if (step.status !== 'pending') {
      return NextResponse.json(
        { error: `Step is in ${step.status} state, not pending` },
        { status: 400 }
      );
    }

    // Get classification API key for moderator
    const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;
    if (!classificationApiKey) {
      return NextResponse.json(
        { error: 'Classification API key not configured' },
        { status: 500 }
      );
    }

    // Initialize moderator
    const moderator = new EnhancedModerator(classificationApiKey, validatedBody.executionId);

    // Update step status to in_progress
    await supabase
      .from('orchestration_steps')
      .update({
        status: 'in_progress',
        started_at: new Date().toISOString()
      })
      .eq('id', step.id);

    // Broadcast moderator assignment message
    const moderatorAssignmentEvent = {
      id: crypto.randomUUID(),
      execution_id: validatedBody.executionId,
      type: 'moderator_assignment' as const,
      timestamp: new Date().toISOString(),
      data: {
        message: generateConversationalMessage('moderator_assignment', step.role_id),
        step: {
          number: step.step_number,
          role: step.role_id,
          model: step.model_name
        }
      },
      step_number: step.step_number,
      role_id: step.role_id,
      model_name: step.model_name
    };
    broadcastOrchestrationEvent(validatedBody.executionId, moderatorAssignmentEvent);

    // Small delay for realistic conversation flow
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Broadcast specialist acknowledgment
    const specialistAckEvent = {
      id: crypto.randomUUID(),
      execution_id: validatedBody.executionId,
      type: 'specialist_acknowledgment' as const,
      timestamp: new Date().toISOString(),
      data: {
        message: generateConversationalMessage('specialist_acknowledgment', step.role_id),
        step: {
          number: step.step_number,
          role: step.role_id,
          model: step.model_name
        }
      },
      step_number: step.step_number,
      role_id: step.role_id,
      model_name: step.model_name
    };
    broadcastOrchestrationEvent(validatedBody.executionId, specialistAckEvent);

    // Another small delay before starting work
    await new Promise(resolve => setTimeout(resolve, 800));

    // Broadcast step started event
    const stepStartedEvent = {
      id: crypto.randomUUID(),
      execution_id: validatedBody.executionId,
      type: 'step_started' as const,
      timestamp: new Date().toISOString(),
      data: {
        commentary: moderator.generateLiveCommentary('step_started', { roleId: step.role_id }),
        step: {
          number: step.step_number,
          role: step.role_id,
          model: step.model_name,
          estimatedDuration: 45000
        }
      },
      step_number: step.step_number,
      role_id: step.role_id,
      model_name: step.model_name
    };
    broadcastOrchestrationEvent(validatedBody.executionId, stepStartedEvent);
      
    // Get the API key for this step
    if (!step.api_key_id) {
      await supabase
        .from('orchestration_steps')
        .update({ 
          status: 'failed',
          error_message: 'No API key assigned to this step'
        })
        .eq('id', step.id);
        
      return NextResponse.json(
        { error: 'No API key assigned to this step' },
        { status: 400 }
      );
    }
    
    const { data: apiKey, error: apiKeyError } = await supabase
      .from('api_keys')
      .select('*')
      .eq('id', step.api_key_id)
      .single();
      
    if (apiKeyError || !apiKey) {
      await supabase
        .from('orchestration_steps')
        .update({ 
          status: 'failed',
          error_message: 'API key not found'
        })
        .eq('id', step.id);
        
      return NextResponse.json(
        { error: 'API key not found' },
        { status: 404 }
      );
    }
    
    // Decrypt the API key
    const decryptedKey = decrypt(apiKey.encrypted_api_key);
    
    // Get previous step output if needed
    let prompt = step.prompt;
    if (prompt.includes('{{previousOutput}}')) {
      // Find the previous step
      const { data: previousStep } = await supabase
        .from('orchestration_steps')
        .select('response')
        .eq('execution_id', step.execution_id)
        .eq('step_number', step.step_number - 1)
        .single();
        
      if (previousStep && previousStep.response) {
        prompt = prompt.replace('{{previousOutput}}', previousStep.response);
      } else {
        prompt = prompt.replace('{{previousOutput}}', 'No previous output available');
      }
    }
    
    // Execute the actual model call with streaming
    const startTime = Date.now();
    let response: string;
    let tokensIn = 0;
    let tokensOut = 0;
    let cost = 0;

    try {
      // Use streaming for better user experience
      const requestPayload = {
        custom_api_config_id: step.execution_id, // Not used by executeProviderRequest but required by schema
        messages: [
          { role: 'user', content: prompt }
        ],
        stream: true, // Enable streaming for real-time updates
        temperature: 0.7,
        max_tokens: 2000
      };

      // Broadcast progress event
      const progressEvent = {
        id: crypto.randomUUID(),
        execution_id: validatedBody.executionId,
        type: 'step_progress' as const,
        timestamp: new Date().toISOString(),
        data: {
          commentary: moderator.generateLiveCommentary('step_progress', { roleId: step.role_id }),
          progress: 0.3,
          status: 'Executing model call...'
        },
        step_number: step.step_number,
        role_id: step.role_id,
        model_name: step.model_name
      };
      broadcastOrchestrationEvent(validatedBody.executionId, progressEvent);

      // Retry logic for provider requests
      let providerResult;
      let lastError;
      const maxRetries = 3;
      let accumulatedStepResponse = '';

      // Define streaming callback for individual steps
      const onStepStreamChunk = (chunk: string) => {
        accumulatedStepResponse += chunk;

        // Broadcast step streaming progress
        const stepStreamEvent = {
          id: crypto.randomUUID(),
          execution_id: validatedBody.executionId,
          type: 'step_streaming' as const,
          timestamp: new Date().toISOString(),
          data: {
            commentary: `💭 ${step.role_id} is thinking...`,
            progress: Math.min(80, (accumulatedStepResponse.length / 500) * 100),
            status: 'Streaming response...',
            partialResponse: accumulatedStepResponse,
            deltaContent: chunk
          },
          step_number: step.step_number,
          role_id: step.role_id,
          model_name: step.model_name
        };
        broadcastOrchestrationEvent(validatedBody.executionId, stepStreamEvent);
      };

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`[Orchestration Retry] Attempt ${attempt}/${maxRetries} for ${step.role_id} step`);

          providerResult = await executeProviderRequest(
            apiKey.provider,
            apiKey.predefined_model_id,
            decryptedKey,
            requestPayload,
            onStepStreamChunk // Pass the streaming callback
          );

          if (providerResult.success) {
            console.log(`[Orchestration Retry] Success on attempt ${attempt} for ${step.role_id} step`);
            break;
          } else {
            lastError = providerResult.error;

            // Check if this is a retryable error
            if (providerResult.error?.retryable && attempt < maxRetries) {
              const backoffMs = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Exponential backoff, max 10s
              console.log(`[Orchestration Retry] Retryable error on attempt ${attempt}, waiting ${backoffMs}ms before retry: ${providerResult.error.message}`);

              // Broadcast retry event
              const retryEvent = {
                id: crypto.randomUUID(),
                execution_id: validatedBody.executionId,
                type: 'step_progress' as const,
                timestamp: new Date().toISOString(),
                data: {
                  commentary: `🔄 ${step.role_id} encountered temporary overload. Retrying in ${Math.round(backoffMs/1000)}s... (attempt ${attempt}/${maxRetries})`,
                  progress: 0.2,
                  status: `Retrying due to: ${providerResult.error.message}`
                },
                step_number: step.step_number,
                role_id: step.role_id,
                model_name: step.model_name
              };
              broadcastOrchestrationEvent(validatedBody.executionId, retryEvent);

              await new Promise(resolve => setTimeout(resolve, backoffMs));
              continue;
            } else {
              throw new Error(providerResult.error?.message || 'Provider request failed');
            }
          }
        } catch (error) {
          lastError = error;
          if (attempt === maxRetries) {
            throw error;
          }

          const backoffMs = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
          console.log(`[Orchestration Retry] Error on attempt ${attempt}, waiting ${backoffMs}ms before retry: ${error}`);
          await new Promise(resolve => setTimeout(resolve, backoffMs));
        }
      }

      if (!providerResult || !providerResult.success) {
        throw new Error(lastError?.message || 'All retry attempts failed');
      }

      // Extract response from provider result
      if (providerResult.responseData?.choices?.[0]?.message?.content) {
        response = providerResult.responseData.choices[0].message.content;
      } else if (providerResult.responseData?.content?.[0]?.text) {
        response = providerResult.responseData.content[0].text;
      } else {
        throw new Error('No valid response content found');
      }

      // Extract token usage and cost
      tokensIn = providerResult.responseData?.usage?.prompt_tokens ||
                 providerResult.responseData?.usage?.input_tokens || 0;
      tokensOut = providerResult.responseData?.usage?.completion_tokens ||
                  providerResult.responseData?.usage?.output_tokens || 0;

      // Simple cost calculation (should match main system)
      cost = (tokensIn * 0.000001) + (tokensOut * 0.000002);

    } catch (error) {
      console.error(`[Process Step] Model call failed: ${error}`);

      // Update step as failed
      await supabase
        .from('orchestration_steps')
        .update({
          status: 'failed',
          completed_at: new Date().toISOString(),
          duration_ms: Date.now() - startTime,
          error_message: error instanceof Error ? error.message : 'Unknown error'
        })
        .eq('id', step.id);

      // Broadcast failure event
      const stepFailedEvent = {
        id: crypto.randomUUID(),
        execution_id: validatedBody.executionId,
        type: 'step_failed' as const,
        timestamp: new Date().toISOString(),
        data: {
          commentary: `❌ ${step.role_id} encountered an issue. Analyzing recovery options...`,
          error: error instanceof Error ? error.message : 'Unknown error',
          retryPlan: 'Manual intervention required'
        },
        step_number: step.step_number,
        role_id: step.role_id,
        model_name: step.model_name
      };
      broadcastOrchestrationEvent(validatedBody.executionId, stepFailedEvent);

      return NextResponse.json(
        { error: 'Model execution failed', details: error instanceof Error ? error.message : 'Unknown error' },
        { status: 500 }
      );
    }

    const duration = Date.now() - startTime;

    // Validate the output with moderator
    const validation = await moderator.validateStepOutput(
      step.step_number,
      step.role_id,
      response,
      prompt,
      `Expected output for ${step.role_id} role`
    );

    // Update the step with the actual response
    await supabase
      .from('orchestration_steps')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString(),
        duration_ms: duration,
        tokens_in: tokensIn,
        tokens_out: tokensOut,
        cost: cost,
        response: response,
        prompt: prompt // Store the processed prompt with replacements
      })
      .eq('id', step.id);

    // Broadcast specialist completion message
    const specialistMessageEvent = {
      id: crypto.randomUUID(),
      execution_id: validatedBody.executionId,
      type: 'specialist_message' as const,
      timestamp: new Date().toISOString(),
      data: {
        message: generateConversationalMessage('specialist_message', step.role_id),
        output: response.length > 200 ? response.substring(0, 200) + '...' : response,
        fullOutput: response,
        duration: duration,
        quality: validation.quality
      },
      step_number: step.step_number,
      role_id: step.role_id,
      model_name: step.model_name
    };
    broadcastOrchestrationEvent(validatedBody.executionId, specialistMessageEvent);

    // Small delay for conversation flow
    await new Promise(resolve => setTimeout(resolve, 1200));

    // Broadcast step completed event
    const stepCompletedEvent = {
      id: crypto.randomUUID(),
      execution_id: validatedBody.executionId,
      type: 'step_completed' as const,
      timestamp: new Date().toISOString(),
      data: {
        commentary: moderator.generateLiveCommentary('step_completed', { roleId: step.role_id }),
        output: response,
        duration: duration,
        tokens: {
          input: tokensIn,
          output: tokensOut
        },
        cost: cost,
        quality: validation.quality,
        validation: validation
      },
      step_number: step.step_number,
      role_id: step.role_id,
      model_name: step.model_name
    };
    broadcastOrchestrationEvent(validatedBody.executionId, stepCompletedEvent);
      
    // Check if this was the last step
    const { data: execution } = await supabase
      .from('orchestration_executions')
      .select('total_steps, created_at')
      .eq('id', step.execution_id)
      .single();
      
    if (execution && step.step_number === execution.total_steps) {
      // This was the last step, trigger final synthesis
      console.log(`[Multi-Role Orchestration] Last step completed! Starting final synthesis for execution ${step.execution_id}`);

      // Get all completed steps for synthesis
      const { data: allSteps } = await supabase
        .from('orchestration_steps')
        .select('step_number, role_id, response, prompt')
        .eq('execution_id', step.execution_id)
        .eq('status', 'completed')
        .order('step_number', { ascending: true });

      if (allSteps && allSteps.length > 0) {
        // Broadcast synthesis started event
        // Use the real-time synthesis stream endpoint for dynamic streaming
        const directStreamUrl = `/api/orchestration/synthesis-stream/${step.execution_id}`;
        console.log(`[Multi-Role Orchestration] Setting up real-time synthesis stream URL: ${directStreamUrl}`);

        const synthesisStartedEvent = {
          id: crypto.randomUUID(),
          execution_id: step.execution_id,
          type: 'synthesis_started' as const,
          timestamp: new Date().toISOString(),
          data: {
            commentary: moderator.generateLiveCommentary('synthesis_started', { totalSteps: allSteps.length }),
            steps: allSteps.length,
            status: 'Synthesizing all specialist outputs...',
            // Include the direct streaming URL for the frontend to connect to
            directStreamUrl: directStreamUrl,
            // Include execution ID for synthesis request
            synthesisExecutionId: step.execution_id
          }
        };

        console.log(`[Multi-Role Orchestration] Broadcasting synthesis_started event with directStreamUrl: ${directStreamUrl}`);
        broadcastOrchestrationEvent(step.execution_id, synthesisStartedEvent);

        // No need to trigger synthesis automatically - the frontend will handle it
        // when it receives the synthesis_started event with the directStreamUrl
        console.log(`[Multi-Role Orchestration] Starting synthesis phase for execution ${step.execution_id}`);
        console.log(`[Multi-Role Orchestration] Frontend will connect to real-time synthesis stream: ${directStreamUrl}`);

        // The actual synthesis will be triggered by the frontend connecting to the
        // real-time synthesis stream endpoint which will start synthesis automatically
      } else {
        // Fallback: mark as completed without synthesis
        await supabase
          .from('orchestration_executions')
          .update({
            status: 'completed',
            completed_at: new Date().toISOString()
          })
          .eq('id', step.execution_id);
      }
    } else if (execution) {
      // Set the next step to pending
      await supabase
        .from('orchestration_steps')
        .update({ status: 'pending' })
        .eq('execution_id', step.execution_id)
        .eq('step_number', step.step_number + 1);

      // Get the next step to process
      const { data: nextStep } = await supabase
        .from('orchestration_steps')
        .select('*')
        .eq('execution_id', step.execution_id)
        .eq('step_number', step.step_number + 1)
        .single();

      if (nextStep) {
        console.log(`[Multi-Role Orchestration] Automatically triggering next step: ${nextStep.role_id} (step ${nextStep.step_number})`);

        // Broadcast moderator handoff message
        const handoffEvent = {
          id: crypto.randomUUID(),
          execution_id: step.execution_id,
          type: 'handoff_message' as const,
          timestamp: new Date().toISOString(),
          data: {
            message: generateConversationalMessage('handoff_message', step.role_id),
            fromRole: step.role_id,
            toRole: nextStep.role_id,
            nextStep: {
              number: nextStep.step_number,
              role: nextStep.role_id,
              model: nextStep.model_name
            }
          },
          step_number: step.step_number,
          role_id: step.role_id,
          model_name: step.model_name
        };
        broadcastOrchestrationEvent(step.execution_id, handoffEvent);

        // Small delay before triggering next step
        setTimeout(() => {
          // Trigger the next step processing asynchronously
          fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/orchestration/process-step`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              executionId: step.execution_id,
              stepId: nextStep.id,
            }),
          }).catch(error => {
            console.error(`[Multi-Role Orchestration] Failed to trigger next step: ${error}`);
          });
        }, 1500); // 1.5 second delay for conversation flow
      }
    }
    
    return NextResponse.json({
      success: true,
      step: {
        id: step.id,
        status: 'completed',
        response: response,
        duration: duration,
        tokens: { input: tokensIn, output: tokensOut },
        cost: cost,
        quality: validation.quality
      }
    });
  } catch (error) {
    console.error(`[Process Step] Error: ${error}`);
    return NextResponse.json(
      { error: 'Internal server error', details: String(error) },
      { status: 500 }
    );
  }
}

