"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.RetrieverServiceClient = void 0;
const jsonProtos = require("../../protos/protos.json");
const google_gax_1 = require("google-gax");
/**
 * Client JSON configuration object, loaded from
 * `src/v1beta/retriever_service_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./retriever_service_client_config.json");
const version = require('../../../package.json').version;
/**
 *  An API for semantic search over a corpus of user uploaded content.
 * @class
 * @memberof v1beta
 */
class RetrieverServiceClient {
    _terminated = false;
    _opts;
    _providedCustomServicePath;
    _gaxModule;
    _gaxGrpc;
    _protos;
    _defaults;
    _universeDomain;
    _servicePath;
    _log = google_gax_1.loggingUtils.log('generativelanguage');
    auth;
    descriptors = {
        page: {},
        stream: {},
        longrunning: {},
        batching: {},
    };
    warn;
    innerApiCalls;
    pathTemplates;
    retrieverServiceStub;
    /**
     * Construct an instance of RetrieverServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new RetrieverServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if (opts?.universe_domain &&
            opts?.universeDomain &&
            opts?.universe_domain !== opts?.universeDomain) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            opts?.universeDomain ??
                opts?.universe_domain ??
                universeDomainEnvVar ??
                'googleapis.com';
        this._servicePath = 'generativelanguage.' + this._universeDomain;
        const servicePath = opts?.servicePath || opts?.apiEndpoint || this._servicePath;
        this._providedCustomServicePath = !!(opts?.servicePath || opts?.apiEndpoint);
        const port = opts?.port || staticMembers.port;
        const clientConfig = opts?.clientConfig ?? {};
        const fallback = opts?.fallback ??
            (typeof window !== 'undefined' && typeof window?.fetch === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            cachedContentPathTemplate: new this._gaxModule.PathTemplate('cachedContents/{id}'),
            chunkPathTemplate: new this._gaxModule.PathTemplate('corpora/{corpus}/documents/{document}/chunks/{chunk}'),
            corpusPathTemplate: new this._gaxModule.PathTemplate('corpora/{corpus}'),
            corpusPermissionPathTemplate: new this._gaxModule.PathTemplate('corpora/{corpus}/permissions/{permission}'),
            documentPathTemplate: new this._gaxModule.PathTemplate('corpora/{corpus}/documents/{document}'),
            filePathTemplate: new this._gaxModule.PathTemplate('files/{file}'),
            modelPathTemplate: new this._gaxModule.PathTemplate('models/{model}'),
            tunedModelPathTemplate: new this._gaxModule.PathTemplate('tunedModels/{tuned_model}'),
            tunedModelPermissionPathTemplate: new this._gaxModule.PathTemplate('tunedModels/{tuned_model}/permissions/{permission}'),
        };
        // Some of the methods on this service return "paged" results,
        // (e.g. 50 results at a time, with tokens to get subsequent
        // pages). Denote the keys used for pagination and results.
        this.descriptors.page = {
            listCorpora: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'corpora'),
            listDocuments: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'documents'),
            listChunks: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'chunks'),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.ai.generativelanguage.v1beta.RetrieverService', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.retrieverServiceStub) {
            return this.retrieverServiceStub;
        }
        // Put together the "service stub" for
        // google.ai.generativelanguage.v1beta.RetrieverService.
        this.retrieverServiceStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.ai.generativelanguage.v1beta.RetrieverService')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.ai.generativelanguage.v1beta
                    .RetrieverService, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const retrieverServiceStubMethods = [
            'createCorpus',
            'getCorpus',
            'updateCorpus',
            'deleteCorpus',
            'listCorpora',
            'queryCorpus',
            'createDocument',
            'getDocument',
            'updateDocument',
            'deleteDocument',
            'listDocuments',
            'queryDocument',
            'createChunk',
            'batchCreateChunks',
            'getChunk',
            'updateChunk',
            'batchUpdateChunks',
            'deleteChunk',
            'batchDeleteChunks',
            'listChunks',
        ];
        for (const methodName of retrieverServiceStubMethods) {
            const callPromise = this.retrieverServiceStub.then(stub => (...args) => {
                if (this._terminated) {
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.page[methodName] || undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.retrieverServiceStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'generativelanguage.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'generativelanguage.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return [];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    createCorpus(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('createCorpus request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('createCorpus response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .createCorpus(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('createCorpus response %j', response);
            return [response, options, rawResponse];
        });
    }
    getCorpus(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('getCorpus request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getCorpus response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .getCorpus(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getCorpus response %j', response);
            return [response, options, rawResponse];
        });
    }
    updateCorpus(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'corpus.name': request.corpus.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('updateCorpus request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('updateCorpus response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .updateCorpus(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('updateCorpus response %j', response);
            return [response, options, rawResponse];
        });
    }
    deleteCorpus(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('deleteCorpus request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('deleteCorpus response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .deleteCorpus(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('deleteCorpus response %j', response);
            return [response, options, rawResponse];
        });
    }
    queryCorpus(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('queryCorpus request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('queryCorpus response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .queryCorpus(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('queryCorpus response %j', response);
            return [response, options, rawResponse];
        });
    }
    createDocument(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('createDocument request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('createDocument response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .createDocument(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('createDocument response %j', response);
            return [response, options, rawResponse];
        });
    }
    getDocument(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('getDocument request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getDocument response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .getDocument(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getDocument response %j', response);
            return [response, options, rawResponse];
        });
    }
    updateDocument(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'document.name': request.document.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('updateDocument request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('updateDocument response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .updateDocument(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('updateDocument response %j', response);
            return [response, options, rawResponse];
        });
    }
    deleteDocument(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('deleteDocument request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('deleteDocument response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .deleteDocument(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('deleteDocument response %j', response);
            return [response, options, rawResponse];
        });
    }
    queryDocument(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('queryDocument request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('queryDocument response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .queryDocument(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('queryDocument response %j', response);
            return [response, options, rawResponse];
        });
    }
    createChunk(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('createChunk request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('createChunk response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .createChunk(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('createChunk response %j', response);
            return [response, options, rawResponse];
        });
    }
    batchCreateChunks(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('batchCreateChunks request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('batchCreateChunks response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .batchCreateChunks(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('batchCreateChunks response %j', response);
            return [response, options, rawResponse];
        });
    }
    getChunk(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('getChunk request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getChunk response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .getChunk(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getChunk response %j', response);
            return [response, options, rawResponse];
        });
    }
    updateChunk(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'chunk.name': request.chunk.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('updateChunk request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('updateChunk response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .updateChunk(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('updateChunk response %j', response);
            return [response, options, rawResponse];
        });
    }
    batchUpdateChunks(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('batchUpdateChunks request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('batchUpdateChunks response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .batchUpdateChunks(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('batchUpdateChunks response %j', response);
            return [response, options, rawResponse];
        });
    }
    deleteChunk(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('deleteChunk request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('deleteChunk response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .deleteChunk(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('deleteChunk response %j', response);
            return [response, options, rawResponse];
        });
    }
    batchDeleteChunks(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('batchDeleteChunks request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('batchDeleteChunks response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .batchDeleteChunks(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('batchDeleteChunks response %j', response);
            return [response, options, rawResponse];
        });
    }
    listCorpora(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listCorpora values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listCorpora request %j', request);
        return this.innerApiCalls
            .listCorpora(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listCorpora values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listCorpora`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Corpora` to return (per page).
     *   The service may return fewer `Corpora`.
     *
     *   If unspecified, at most 10 `Corpora` will be returned.
     *   The maximum size limit is 20 `Corpora` per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListCorpora` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListCorpora`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1beta.Corpus|Corpus} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listCorporaAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listCorporaStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        const defaultCallSettings = this._defaults['listCorpora'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listCorpora stream %j', request);
        return this.descriptors.page.listCorpora.createStream(this.innerApiCalls.listCorpora, request, callSettings);
    }
    /**
     * Equivalent to `listCorpora`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Corpora` to return (per page).
     *   The service may return fewer `Corpora`.
     *
     *   If unspecified, at most 10 `Corpora` will be returned.
     *   The maximum size limit is 20 `Corpora` per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListCorpora` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListCorpora`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1beta.Corpus|Corpus}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.list_corpora.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_ListCorpora_async
     */
    listCorporaAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        const defaultCallSettings = this._defaults['listCorpora'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listCorpora iterate %j', request);
        return this.descriptors.page.listCorpora.asyncIterate(this.innerApiCalls['listCorpora'], request, callSettings);
    }
    listDocuments(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listDocuments values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listDocuments request %j', request);
        return this.innerApiCalls
            .listDocuments(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listDocuments values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listDocuments`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Corpus` containing `Document`s.
     *   Example: `corpora/my-corpus-123`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Document`s to return (per page).
     *   The service may return fewer `Document`s.
     *
     *   If unspecified, at most 10 `Document`s will be returned.
     *   The maximum size limit is 20 `Document`s per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListDocuments` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListDocuments`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1beta.Document|Document} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listDocumentsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listDocumentsStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        const defaultCallSettings = this._defaults['listDocuments'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listDocuments stream %j', request);
        return this.descriptors.page.listDocuments.createStream(this.innerApiCalls.listDocuments, request, callSettings);
    }
    /**
     * Equivalent to `listDocuments`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Corpus` containing `Document`s.
     *   Example: `corpora/my-corpus-123`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Document`s to return (per page).
     *   The service may return fewer `Document`s.
     *
     *   If unspecified, at most 10 `Document`s will be returned.
     *   The maximum size limit is 20 `Document`s per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListDocuments` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListDocuments`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1beta.Document|Document}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.list_documents.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_ListDocuments_async
     */
    listDocumentsAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        const defaultCallSettings = this._defaults['listDocuments'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listDocuments iterate %j', request);
        return this.descriptors.page.listDocuments.asyncIterate(this.innerApiCalls['listDocuments'], request, callSettings);
    }
    listChunks(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listChunks values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listChunks request %j', request);
        return this.innerApiCalls
            .listChunks(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listChunks values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listChunks`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Document` containing `Chunk`s.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Chunk`s to return (per page).
     *   The service may return fewer `Chunk`s.
     *
     *   If unspecified, at most 10 `Chunk`s will be returned.
     *   The maximum size limit is 100 `Chunk`s per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListChunks` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListChunks`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.ai.generativelanguage.v1beta.Chunk|Chunk} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listChunksAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listChunksStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        const defaultCallSettings = this._defaults['listChunks'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listChunks stream %j', request);
        return this.descriptors.page.listChunks.createStream(this.innerApiCalls.listChunks, request, callSettings);
    }
    /**
     * Equivalent to `listChunks`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the `Document` containing `Chunk`s.
     *   Example: `corpora/my-corpus-123/documents/the-doc-abc`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of `Chunk`s to return (per page).
     *   The service may return fewer `Chunk`s.
     *
     *   If unspecified, at most 10 `Chunk`s will be returned.
     *   The maximum size limit is 100 `Chunk`s per page.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListChunks` call.
     *
     *   Provide the `next_page_token` returned in the response as an argument to
     *   the next request to retrieve the next page.
     *
     *   When paginating, all other parameters provided to `ListChunks`
     *   must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.ai.generativelanguage.v1beta.Chunk|Chunk}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta/retriever_service.list_chunks.js</caption>
     * region_tag:generativelanguage_v1beta_generated_RetrieverService_ListChunks_async
     */
    listChunksAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        const defaultCallSettings = this._defaults['listChunks'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listChunks iterate %j', request);
        return this.descriptors.page.listChunks.asyncIterate(this.innerApiCalls['listChunks'], request, callSettings);
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified cachedContent resource name string.
     *
     * @param {string} id
     * @returns {string} Resource name string.
     */
    cachedContentPath(id) {
        return this.pathTemplates.cachedContentPathTemplate.render({
            id: id,
        });
    }
    /**
     * Parse the id from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the id.
     */
    matchIdFromCachedContentName(cachedContentName) {
        return this.pathTemplates.cachedContentPathTemplate.match(cachedContentName)
            .id;
    }
    /**
     * Return a fully-qualified chunk resource name string.
     *
     * @param {string} corpus
     * @param {string} document
     * @param {string} chunk
     * @returns {string} Resource name string.
     */
    chunkPath(corpus, document, chunk) {
        return this.pathTemplates.chunkPathTemplate.render({
            corpus: corpus,
            document: document,
            chunk: chunk,
        });
    }
    /**
     * Parse the corpus from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromChunkName(chunkName) {
        return this.pathTemplates.chunkPathTemplate.match(chunkName).corpus;
    }
    /**
     * Parse the document from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromChunkName(chunkName) {
        return this.pathTemplates.chunkPathTemplate.match(chunkName).document;
    }
    /**
     * Parse the chunk from Chunk resource.
     *
     * @param {string} chunkName
     *   A fully-qualified path representing Chunk resource.
     * @returns {string} A string representing the chunk.
     */
    matchChunkFromChunkName(chunkName) {
        return this.pathTemplates.chunkPathTemplate.match(chunkName).chunk;
    }
    /**
     * Return a fully-qualified corpus resource name string.
     *
     * @param {string} corpus
     * @returns {string} Resource name string.
     */
    corpusPath(corpus) {
        return this.pathTemplates.corpusPathTemplate.render({
            corpus: corpus,
        });
    }
    /**
     * Parse the corpus from Corpus resource.
     *
     * @param {string} corpusName
     *   A fully-qualified path representing Corpus resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromCorpusName(corpusName) {
        return this.pathTemplates.corpusPathTemplate.match(corpusName).corpus;
    }
    /**
     * Return a fully-qualified corpusPermission resource name string.
     *
     * @param {string} corpus
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    corpusPermissionPath(corpus, permission) {
        return this.pathTemplates.corpusPermissionPathTemplate.render({
            corpus: corpus,
            permission: permission,
        });
    }
    /**
     * Parse the corpus from CorpusPermission resource.
     *
     * @param {string} corpusPermissionName
     *   A fully-qualified path representing corpus_permission resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromCorpusPermissionName(corpusPermissionName) {
        return this.pathTemplates.corpusPermissionPathTemplate.match(corpusPermissionName).corpus;
    }
    /**
     * Parse the permission from CorpusPermission resource.
     *
     * @param {string} corpusPermissionName
     *   A fully-qualified path representing corpus_permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromCorpusPermissionName(corpusPermissionName) {
        return this.pathTemplates.corpusPermissionPathTemplate.match(corpusPermissionName).permission;
    }
    /**
     * Return a fully-qualified document resource name string.
     *
     * @param {string} corpus
     * @param {string} document
     * @returns {string} Resource name string.
     */
    documentPath(corpus, document) {
        return this.pathTemplates.documentPathTemplate.render({
            corpus: corpus,
            document: document,
        });
    }
    /**
     * Parse the corpus from Document resource.
     *
     * @param {string} documentName
     *   A fully-qualified path representing Document resource.
     * @returns {string} A string representing the corpus.
     */
    matchCorpusFromDocumentName(documentName) {
        return this.pathTemplates.documentPathTemplate.match(documentName).corpus;
    }
    /**
     * Parse the document from Document resource.
     *
     * @param {string} documentName
     *   A fully-qualified path representing Document resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromDocumentName(documentName) {
        return this.pathTemplates.documentPathTemplate.match(documentName).document;
    }
    /**
     * Return a fully-qualified file resource name string.
     *
     * @param {string} file
     * @returns {string} Resource name string.
     */
    filePath(file) {
        return this.pathTemplates.filePathTemplate.render({
            file: file,
        });
    }
    /**
     * Parse the file from File resource.
     *
     * @param {string} fileName
     *   A fully-qualified path representing File resource.
     * @returns {string} A string representing the file.
     */
    matchFileFromFileName(fileName) {
        return this.pathTemplates.filePathTemplate.match(fileName).file;
    }
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(model) {
        return this.pathTemplates.modelPathTemplate.render({
            model: model,
        });
    }
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).model;
    }
    /**
     * Return a fully-qualified tunedModel resource name string.
     *
     * @param {string} tuned_model
     * @returns {string} Resource name string.
     */
    tunedModelPath(tunedModel) {
        return this.pathTemplates.tunedModelPathTemplate.render({
            tuned_model: tunedModel,
        });
    }
    /**
     * Parse the tuned_model from TunedModel resource.
     *
     * @param {string} tunedModelName
     *   A fully-qualified path representing TunedModel resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelName(tunedModelName) {
        return this.pathTemplates.tunedModelPathTemplate.match(tunedModelName)
            .tuned_model;
    }
    /**
     * Return a fully-qualified tunedModelPermission resource name string.
     *
     * @param {string} tuned_model
     * @param {string} permission
     * @returns {string} Resource name string.
     */
    tunedModelPermissionPath(tunedModel, permission) {
        return this.pathTemplates.tunedModelPermissionPathTemplate.render({
            tuned_model: tunedModel,
            permission: permission,
        });
    }
    /**
     * Parse the tuned_model from TunedModelPermission resource.
     *
     * @param {string} tunedModelPermissionName
     *   A fully-qualified path representing tuned_model_permission resource.
     * @returns {string} A string representing the tuned_model.
     */
    matchTunedModelFromTunedModelPermissionName(tunedModelPermissionName) {
        return this.pathTemplates.tunedModelPermissionPathTemplate.match(tunedModelPermissionName).tuned_model;
    }
    /**
     * Parse the permission from TunedModelPermission resource.
     *
     * @param {string} tunedModelPermissionName
     *   A fully-qualified path representing tuned_model_permission resource.
     * @returns {string} A string representing the permission.
     */
    matchPermissionFromTunedModelPermissionName(tunedModelPermissionName) {
        return this.pathTemplates.tunedModelPermissionPathTemplate.match(tunedModelPermissionName).permission;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.retrieverServiceStub && !this._terminated) {
            return this.retrieverServiceStub.then(stub => {
                this._log.info('ending gRPC channel');
                this._terminated = true;
                stub.close();
            });
        }
        return Promise.resolve();
    }
}
exports.RetrieverServiceClient = RetrieverServiceClient;
//# sourceMappingURL=retriever_service_client.js.map