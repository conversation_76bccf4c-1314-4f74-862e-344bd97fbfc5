import { type NextRequest, NextResponse } from 'next/server';

// Environment variable for the master API access token
const ROKEY_API_ACCESS_TOKEN = process.env.ROKEY_API_ACCESS_TOKEN;
const INTERNAL_COMPLETIONS_ENDPOINT = '/api/v1/chat/completions'; // Relative path to the internal API

export async function POST(request: NextRequest) {
  // TODO: STEP 1: User Authentication/Session Validation (Future Implementation)
  // For now, this endpoint is accessible to any client that can reach the Next.js application.
  // Example:
  // const session = await getServerSession(authOptions); // Using NextAuth.js
  // if (!session) {
  //   return NextResponse.json({ error: 'Unauthorized: User session required.' }, { status: 401 });
  // }

  if (!ROKEY_API_ACCESS_TOKEN) {
    console.error('[API Playground Proxy] ROKEY_API_ACCESS_TOKEN is not set in environment variables.');
    return NextResponse.json({ error: 'Server configuration error: Master API token not configured.' }, { status: 500 });
  }

  try {
    const requestBody = await request.json();
    const stream = requestBody.stream === true;

    // Construct the absolute URL for the internal API call
    // NextRequest.url gives the full URL of the incoming request to *this* (playground) route.
    // We can use its origin to build the full path to the internal completions API.
    const internalApiUrl = new URL(INTERNAL_COMPLETIONS_ENDPOINT, request.url).toString();
    
    console.log(`[API Playground Proxy] Forwarding request to: ${internalApiUrl}`);

    const response = await fetch(internalApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${ROKEY_API_ACCESS_TOKEN}`,
      },
      body: JSON.stringify(requestBody),
      // Important for Next.js fetch in Route Handlers if you want to avoid caching POST requests
      cache: 'no-store',
      // duplex: 'half' is needed if the downstream service streams and this route also needs to stream
      // and you are using Node.js 18+. For Vercel, this is typically handled.
      // If issues with streaming, uncomment the following:
      // ...(stream && { duplex: 'half' } as any),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: { message: response.statusText } }));
      console.error(`[API Playground Proxy] Error from internal completions API (${response.status}):`, errorData);
      return NextResponse.json(
        { error: `Error from backend completions API: ${errorData?.error?.message || response.statusText}` },
        { status: response.status }
      );
    }

    if (stream && response.body) {
      // If the response is streaming, return the stream directly.
      // The headers from the internal API should be mostly correct (e.g., Content-Type: text/event-stream)
      // We might want to copy relevant headers if necessary.
      const responseHeaders = new Headers(response.headers); // Copy headers
      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
      });
    } else {
      // If not streaming, parse and return JSON
      const jsonData = await response.json();
      return NextResponse.json(jsonData, { status: response.status });
    }

  } catch (error: Error) {
    console.error('[API Playground Proxy] Error processing request:', error);
    let errorMessage = 'An unexpected error occurred in the playground proxy.';
    if (error instanceof SyntaxError) {
        errorMessage = 'Invalid JSON in request body.';
    } else if (error.message) {
        errorMessage = error.message;
    }
    return NextResponse.json({ error: errorMessage, details: error.toString() }, { status: 500 });
  }
}

export async function OPTIONS(request: NextRequest) {
  // Basic CORS for the playground proxy. Adjust origins as needed for production.
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*', // Or your frontend domain
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type', // No Authorization needed from client
    },
  });
} 