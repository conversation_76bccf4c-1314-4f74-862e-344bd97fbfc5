"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextServiceClient = exports.RetrieverServiceClient = exports.PredictionServiceClient = exports.PermissionServiceClient = exports.ModelServiceClient = exports.GenerativeServiceClient = exports.FileServiceClient = exports.DiscussServiceClient = exports.CacheServiceClient = void 0;
var cache_service_client_1 = require("./cache_service_client");
Object.defineProperty(exports, "CacheServiceClient", { enumerable: true, get: function () { return cache_service_client_1.CacheServiceClient; } });
var discuss_service_client_1 = require("./discuss_service_client");
Object.defineProperty(exports, "DiscussServiceClient", { enumerable: true, get: function () { return discuss_service_client_1.DiscussServiceClient; } });
var file_service_client_1 = require("./file_service_client");
Object.defineProperty(exports, "FileServiceClient", { enumerable: true, get: function () { return file_service_client_1.FileServiceClient; } });
var generative_service_client_1 = require("./generative_service_client");
Object.defineProperty(exports, "GenerativeServiceClient", { enumerable: true, get: function () { return generative_service_client_1.GenerativeServiceClient; } });
var model_service_client_1 = require("./model_service_client");
Object.defineProperty(exports, "ModelServiceClient", { enumerable: true, get: function () { return model_service_client_1.ModelServiceClient; } });
var permission_service_client_1 = require("./permission_service_client");
Object.defineProperty(exports, "PermissionServiceClient", { enumerable: true, get: function () { return permission_service_client_1.PermissionServiceClient; } });
var prediction_service_client_1 = require("./prediction_service_client");
Object.defineProperty(exports, "PredictionServiceClient", { enumerable: true, get: function () { return prediction_service_client_1.PredictionServiceClient; } });
var retriever_service_client_1 = require("./retriever_service_client");
Object.defineProperty(exports, "RetrieverServiceClient", { enumerable: true, get: function () { return retriever_service_client_1.RetrieverServiceClient; } });
var text_service_client_1 = require("./text_service_client");
Object.defineProperty(exports, "TextServiceClient", { enumerable: true, get: function () { return text_service_client_1.TextServiceClient; } });
//# sourceMappingURL=index.js.map